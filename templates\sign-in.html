<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
    <title>Sign In - Product Analyzer</title>
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
    
    <!-- All styles are inlined here from the "prettier" template -->
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }
        .bg-animation {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            overflow: hidden; z-index: 0;
        }
        .floating-shape {
            position: absolute; background: rgba(255, 255, 255, 0.1);
            border-radius: 50%; animation: float 6s ease-in-out infinite;
        }
        .shape-1 { width: 80px; height: 80px; top: 20%; left: 10%; animation-delay: 0s; }
        .shape-2 { width: 120px; height: 120px; top: 60%; right: 15%; animation-delay: 2s; }
        .shape-3 { width: 60px; height: 60px; bottom: 20%; left: 20%; animation-delay: 4s; }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px);
            border-radius: 24px; padding: 40px; width: 100%; max-width: 420px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative; z-index: 1; animation: slideUp 0.8s ease-out;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .logo-section { text-align: center; margin-bottom: 32px; }
        .logo-icon {
            width: 64px; height: 64px; background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 16px; display: inline-flex; align-items: center;
            justify-content: center; margin-bottom: 16px;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }
        .logo-icon svg { width: 32px; height: 32px; fill: white; }
        .app-title {
            font-size: 28px; font-weight: 700; color: #1a202c; margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .app-subtitle { font-size: 16px; color: #718096; font-weight: 400; }
        .form-group { margin-bottom: 24px; position: relative; }
        .form-label { display: block; font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 8px; }
        .form-input {
            width: 100%; padding: 16px 20px; border: 2px solid #e5e7eb;
            border-radius: 12px; font-size: 16px; transition: all 0.3s ease;
            background: #fafafa; color: #374151;
        }
        .form-input:focus {
            outline: none; border-color: #667eea; background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .form-input::placeholder { color: #9ca3af; }
        .password-toggle {
            position: absolute; right: 16px; top: 50%; transform: translateY(-50%);
            background: none; border: none; cursor: pointer; color: #6b7280;
            padding: 4px; border-radius: 4px; transition: color 0.2s ease;
        }
        .password-toggle:hover { color: #667eea; }
        .text-center { text-align: center; }
        .signup-link { text-align: center; font-size: 14px; color: #6b7280; margin-top: 32px; }
        .signup-link a {
            color: #667eea; text-decoration: none; font-weight: 600;
            transition: color 0.2s ease;
        }
        .signup-link a:hover { color: #764ba2; text-decoration: underline; }
        
        /* Button Styles */
        .login-btn {
            width: 100%; padding: 16px; background: linear-gradient(135deg, #667eea, #764ba2);
            color: white; border: none; border-radius: 12px; font-size: 16px;
            font-weight: 600; cursor: pointer; transition: all 0.3s ease;
            position: relative; overflow: hidden;
        }
        .login-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4); }
        .login-btn:active { transform: translateY(0); }
        .login-btn.loading { pointer-events: none; }
        .btn-text { transition: opacity 0.3s ease; }
        .loading-spinner {
            position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 20px; height: 20px; border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white; border-radius: 50%;
            animation: spin 1s linear infinite; opacity: 0; transition: opacity 0.3s ease;
        }
        .login-btn.loading .btn-text { opacity: 0; }
        .login-btn.loading .loading-spinner { opacity: 1; }
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        /* Alert and Error Styles */
        .alert-message {
            padding: 12px 16px; border-radius: 8px; font-size: 14px; margin-bottom: 20px;
        }
        .alert-success { background: #d1fae5; color: #065f46; border-left: 4px solid #10b981; }
        .alert-danger { background: #fee2e2; color: #dc2626; border-left: 4px solid #dc2626; }
        .field-error { color: #dc2626; font-size: 12px; margin-top: 6px; display: block; }
        .form-input.error { border-color: #dc2626; background-color: #fef2f2; }
        
        @media (max-width: 480px) {
            .login-container { padding: 24px; margin: 10px; }
            .app-title { font-size: 24px; }
        }
    </style>
</head>
<body>
    <div class="bg-animation">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
    </div>

    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <svg viewBox="0 0 24 24">
                    <path d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10,10-4.48,10-10S17.52,2,12,2z M17,9.59L15.59,8.17L12,11.76L8.41,8.17L7,9.59 l5,5L17,9.59z" transform="rotate(180 12 12)"/>
                </svg>
            </div>
            <h1 class="app-title">Product Analyzer</h1>
            <p class="app-subtitle">Welcome back! Please sign in.</p>
        </div>

        <!-- FLASK FLASH MESSAGES -->
        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              <div class="alert-message alert-{{ category }}" role="alert">
                {{ message }}
              </div>
            {% endfor %}
          {% endif %}
        {% endwith %}

        <!-- LOGIN FORM - Integrated with Flask-WTForms -->
        <form method="POST" action="{{ url_for('login') }}" id="loginForm" novalidate>
            {{ form.hidden_tag() }} <!-- CSRF Protection -->

            <div class="form-group">
                {{ form.email.label(class="form-label") }}
                <!-- The 'error' class is added dynamically if WTForms finds an error -->
                {{ form.email(
                    class="form-input" + (" error" if form.email.errors else ""), 
                    placeholder="<EMAIL>"
                ) }}

                <!-- Field-specific errors from WTForms -->
                {% if form.email.errors %}
                    {% for error in form.email.errors %}
                        <div class="field-error">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="form-group">
                {{ form.password.label(class="form-label") }}
                <div style="position: relative;">
                    {{ form.password(
                        class="form-input" + (" error" if form.password.errors else ""), 
                        placeholder="Your Password"
                    ) }}
                    <button type="button" class="password-toggle" id="togglePassword">👁️</button>
                </div>
                {% if form.password.errors %}
                    {% for error in form.password.errors %}
                        <div class="field-error">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="text-center">
                <!-- Use a standard button tag for better styling control -->
                <button type="submit" class="login-btn" id="loginBtn">
                    <span class="btn-text">Sign In</span>
                    <div class="loading-spinner"></div>
                </button>
            </div>
        </form>

        <div class="signup-link">
            Don't have an account? <a href="{{ url_for('register') }}">Sign up</a>
        </div>
    </div>

    <script>
        // All JavaScript is self-contained and updated to support the Flask form
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const passwordInput = document.getElementById('password'); // WTForms sets this ID automatically
            const togglePassword = document.getElementById('togglePassword');
            const loginBtn = document.getElementById('loginBtn');
            
            // Password toggle functionality
            if(togglePassword) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    this.textContent = type === 'password' ? '👁️' : '🙈';
                });
            }
            
            // On form submission, show the loading spinner
            if(loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    // Basic client-side check to prevent submitting an empty form
                    const emailInput = document.getElementById('email');
                    if (emailInput.value.trim() === '' || passwordInput.value.trim() === '') {
                        // Let server-side validation handle detailed error messages
                        // but prevent the loading state on an obviously empty form.
                        return; 
                    }
                    
                    if(loginBtn) {
                        loginBtn.classList.add('loading');
                    }
                });
            }
        });
    </script>
</body>
</html>