<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Sign In - Product Analyzer</title>
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />
</head>
<body class="">
  <main class="main-content  mt-0">
    <section>
      <div class="page-header min-vh-75">
        <div class="container">
          <div class="row">
            <div class="col-xl-4 col-lg-5 col-md-6 d-flex flex-column mx-auto">
              <div class="card card-plain mt-8">
                <div class="card-header pb-0 text-left bg-transparent">
                  <h3 class="font-weight-bolder text-info text-gradient">Welcome back</h3>
                  <p class="mb-0">Enter your email and password to sign in</p>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                  {% if messages %}
                    {% for category, message in messages %}
                      <div class="alert alert-{{ category }} alert-dismissible fade show text-white" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                      </div>
                    {% endfor %}
                  {% endif %}
                {% endwith %}

                <div class="card-body">
                  <form role="form" method="POST" action="{{ url_for('login') }}">
                    {{ form.hidden_tag() }} <!-- CSRF protection -->
                    
                    <div class="mb-3">
                      {{ form.email.label(class="form-label") }}
                      {{ form.email(class="form-control", placeholder="Email", **{'aria-label': 'Email', 'aria-describedby': 'email-addon'}) }}
                       {% if form.email.errors %}
                          {% for error in form.email.errors %}
                              <small class="text-danger">{{ error }}</small>
                          {% endfor %}
                      {% endif %}
                    </div>
                    
                    <div class="mb-3">
                      {{ form.password.label(class="form-label") }}
                      {{ form.password(class="form-control", placeholder="Password", **{'aria-label': 'Password', 'aria-describedby': 'password-addon'}) }}
                       {% if form.password.errors %}
                          {% for error in form.password.errors %}
                              <small class="text-danger">{{ error }}</small>
                          {% endfor %}
                      {% endif %}
                    </div>
                    
                    <div class="text-center">
                      {{ form.submit(class="btn bg-gradient-info w-100 mt-4 mb-0") }}
                    </div>
                  </form>
                </div>
                <div class="card-footer text-center pt-0 px-lg-2 px-1">
                  <p class="mb-4 text-sm mx-auto">
                    Don't have an account?
                    <a href="{{ url_for('register') }}" class="text-info text-gradient font-weight-bold">Sign up</a>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="oblique position-absolute top-0 h-100 d-md-block d-none me-n8">
                <div class="oblique-image bg-cover position-absolute fixed-top ms-auto h-100 z-index-0 ms-n6" style="background-image:url('{{ url_for('static', filename='assets/img/curved-images/curved6.jpg') }}')"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
  <script src="{{ url_for('static', filename='assets/js/core/bootstrap.min.js') }}"></script>
</body>
</html>