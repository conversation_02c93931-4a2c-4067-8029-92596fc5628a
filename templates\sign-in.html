<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Sign In - Product Analyzer</title>
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />
  <style>
    /* Enhanced Sign-In Page Styles */
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Inter', sans-serif;
    }

    .page-header {
      background: transparent;
    }

    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border: none;
      border-radius: 25px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-10px);
      box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4);
    }

    .card-header {
      background: transparent !important;
      border: none;
      padding: 30px 30px 0 30px;
    }

    .card-body {
      padding: 30px;
    }

    h3 {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: 800;
      font-size: 2.5rem;
      margin-bottom: 10px;
    }

    .form-control {
      border: 2px solid #e9ecef;
      border-radius: 15px;
      padding: 15px 20px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus {
      border-color: #667eea;
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
      transform: translateY(-2px);
      background: white;
    }

    .form-label {
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 25px;
      padding: 15px 30px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
      width: 100%;
    }

    .btn-primary:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    }

    .alert {
      border: none;
      border-radius: 15px;
      padding: 15px 20px;
      margin: 20px 0;
      font-weight: 500;
    }

    .alert-danger {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
      color: white;
    }

    .alert-success {
      background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
      color: white;
    }

    .text-danger {
      color: #ff6b6b !important;
      font-weight: 500;
    }

    p {
      color: #6c757d;
      font-weight: 500;
    }

    a {
      color: #667eea;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    a:hover {
      color: #764ba2;
      text-decoration: underline;
    }

    /* Animated Background Elements */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx=".5" cy=".5" r=".5"><stop offset="0" stop-color="%23ffffff" stop-opacity=".1"/><stop offset="1" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="20" cy="20" r="10" fill="url(%23a)"/><circle cx="80" cy="80" r="15" fill="url(%23a)"/><circle cx="40" cy="70" r="8" fill="url(%23a)"/><circle cx="90" cy="30" r="12" fill="url(%23a)"/><circle cx="10" cy="90" r="6" fill="url(%23a)"/></svg>');
      opacity: 0.3;
      z-index: -1;
      animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }

    /* Form Animation */
    .card {
      animation: slideInUp 0.8s ease-out;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(50px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>
<body class="">
  <main class="main-content  mt-0">
    <section>
      <div class="page-header min-vh-75">
        <div class="container">
          <div class="row">
            <div class="col-xl-4 col-lg-5 col-md-6 d-flex flex-column mx-auto">
              <div class="card card-plain mt-8">
                <div class="card-header pb-0 text-left bg-transparent">
                  <h3 class="font-weight-bolder text-info text-gradient">Welcome back</h3>
                  <p class="mb-0">Enter your email and password to sign in</p>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                  {% if messages %}
                    {% for category, message in messages %}
                      <div class="alert alert-{{ category }} alert-dismissible fade show text-white" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                      </div>
                    {% endfor %}
                  {% endif %}
                {% endwith %}

                <div class="card-body">
                  <form role="form" method="POST" action="{{ url_for('login') }}">
                    {{ form.hidden_tag() }} <!-- CSRF protection -->
                    
                    <div class="mb-3">
                      {{ form.email.label(class="form-label") }}
                      {{ form.email(class="form-control", placeholder="Email", **{'aria-label': 'Email', 'aria-describedby': 'email-addon'}) }}
                       {% if form.email.errors %}
                          {% for error in form.email.errors %}
                              <small class="text-danger">{{ error }}</small>
                          {% endfor %}
                      {% endif %}
                    </div>
                    
                    <div class="mb-3">
                      {{ form.password.label(class="form-label") }}
                      {{ form.password(class="form-control", placeholder="Password", **{'aria-label': 'Password', 'aria-describedby': 'password-addon'}) }}
                       {% if form.password.errors %}
                          {% for error in form.password.errors %}
                              <small class="text-danger">{{ error }}</small>
                          {% endfor %}
                      {% endif %}
                    </div>
                    
                    <div class="text-center">
                      {{ form.submit(class="btn bg-gradient-info w-100 mt-4 mb-0") }}
                    </div>
                  </form>
                </div>
                <div class="card-footer text-center pt-0 px-lg-2 px-1">
                  <p class="mb-4 text-sm mx-auto">
                    Don't have an account?
                    <a href="{{ url_for('register') }}" class="text-info text-gradient font-weight-bold">Sign up</a>
                  </p>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="oblique position-absolute top-0 h-100 d-md-block d-none me-n8">
                <div class="oblique-image bg-cover position-absolute fixed-top ms-auto h-100 z-index-0 ms-n6" style="background-image:url('{{ url_for('static', filename='assets/img/curved-images/curved6.jpg') }}')"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
  <script src="{{ url_for('static', filename='assets/js/core/bootstrap.min.js') }}"></script>
</body>
</html>