document.addEventListener('DOMContentLoaded', function () {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const comparisonResults = document.getElementById('comparisonResults');
    
    const chartInstances = {};

    const urlParams = new URLSearchParams(window.location.search);
    const product1Name = urlParams.get('product1');
    const product2Name = urlParams.get('product2');

    async function fetchAndDisplayComparison() {
        if (!product1Name || !product2Name) {
            console.error("Missing product names in URL.");
            loadingOverlay.innerHTML = '<div class="alert alert-danger" role="alert">Error: Missing product names for comparison. Please go back and try again.</div>';
            return;
        }

        try {
            const response = await fetch(`/compare_data?product1=${encodeURIComponent(product1Name)}&product2=${encodeURIComponent(product2Name)}`);
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Server error: ${response.status}`);
            }
            const data = await response.json();
            
            loadingOverlay.style.display = 'none';
            comparisonResults.style.display = 'block';

            populateColumn('1', data.product1);
            populateColumn('2', data.product2);

        } catch (error) {
            console.error('Error fetching comparison data:', error);
            loadingOverlay.innerHTML = `<div class="alert alert-danger" role="alert">Failed to load comparison data: ${error.message}</div>`;
        }
    }

    function populateColumn(colNum, productData) {
        // Headers and Image
        document.getElementById(`product${colNum}NameHeader`).textContent = productData.product_name;
        const imageElement = document.getElementById(`product${colNum}Image`);
        const noImageElement = document.getElementById(`noImage${colNum}Text`);
        if (productData.product_image_url) {
            imageElement.src = productData.product_image_url;
            imageElement.style.display = 'block';
            noImageElement.style.display = 'none';
        } else {
            imageElement.style.display = 'none';
            noImageElement.style.display = 'block';
        }

        document.getElementById(`tweets${colNum}Count`).textContent = productData.tweets_count;

        // --- NEW: Populate Specifications Snippet ---
        const specSnippetElement = document.getElementById(`product${colNum}SpecSnippet`);
        specSnippetElement.innerHTML = ''; // Clear placeholder
        if (productData.product_specifications_snippet && !productData.product_specifications_snippet.toLowerCase().includes("not found")) {
            let snippetContent = productData.product_specifications_snippet;
            // Try to format it as a list if it contains common delimiters
            const potentialListItems = snippetContent.split(/\s*•\s*|\s*-\s*|\s*\*\s*|\.{3,}|‣/);
            if (potentialListItems.length > 2 && potentialListItems.some(item => item.trim().length > 5)) {
                const ul = document.createElement('ul');
                ul.className = 'list-unstyled ps-0 text-start';
                potentialListItems.forEach(line => {
                    line = line.trim();
                    if (line) {
                        const li = document.createElement('li');
                        const colonIndex = line.indexOf(':');
                        if (colonIndex > 0 && colonIndex < 40 && !line.substring(0, colonIndex).includes('\n')) {
                            li.innerHTML = `<strong>${line.substring(0, colonIndex).trim()}:</strong> ${line.substring(colonIndex + 1).trim()}`;
                        } else {
                           li.textContent = line;
                        }
                        ul.appendChild(li);
                    }
                });
                if (ul.children.length > 0) {
                    specSnippetElement.appendChild(ul);
                } else { // Fallback if splitting resulted in nothing useful
                     const p = document.createElement('p');
                     p.textContent = snippetContent;
                     specSnippetElement.appendChild(p);
                }
            } else { // If not a list, show as paragraphs
                const paragraphs = snippetContent.split('\n');
                paragraphs.forEach(paraText => {
                    if (paraText.trim()) {
                        const p = document.createElement('p');
                        p.textContent = paraText.trim();
                        p.className = 'text-start';
                        specSnippetElement.appendChild(p);
                    }
                });
            }
        } else {
            specSnippetElement.innerHTML = '<p style="color: #6c757d;">Specifications data not available.</p>';
        }


        // Destroy old charts if they exist
        if (chartInstances[`overall${colNum}`]) chartInstances[`overall${colNum}`].destroy();
        if (chartInstances[`aspect${colNum}`]) chartInstances[`aspect${colNum}`].destroy();

        // Overall Sentiment Chart (Pie)
        const overallCtx = document.getElementById(`overall${colNum}Chart`).getContext('2d');
        const overallSentiment = productData.overall_sentiment;
        chartInstances[`overall${colNum}`] = new Chart(overallCtx, {
            type: 'pie',
            data: {
                labels: ['Positive', 'Negative', 'Neutral'],
                datasets: [{
                    data: [overallSentiment.positive, overallSentiment.negative, overallSentiment.neutral],
                    backgroundColor: ['rgba(75, 192, 192, 0.7)', 'rgba(255, 99, 132, 0.7)', 'rgba(201, 203, 207, 0.7)']
                }]
            },
            options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'top' } } }
        });

        // Aspect Sentiment Chart (Radar)
        const aspectCtx = document.getElementById(`aspect${colNum}Chart`).getContext('2d');
        const aspectSentiments = productData.aspect_sentiments;
        const aspectLabels = Object.keys(aspectSentiments);
        const positiveData = aspectLabels.map(label => aspectSentiments[label].positive);
        const negativeData = aspectLabels.map(label => aspectSentiments[label].negative);
        
        chartInstances[`aspect${colNum}`] = new Chart(aspectCtx, {
            type: 'radar',
            data: {
                labels: aspectLabels.map(l => l.charAt(0).toUpperCase() + l.slice(1)),
                datasets: [
                    {
                        label: 'Positive Mentions',
                        data: positiveData,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                    },
                    {
                        label: 'Negative Mentions',
                        data: negativeData,
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { position: 'bottom' } },
                scales: { r: { beginAtZero: true, ticks: { stepSize: 1 } } }
            }
        });
    }

    // --- Initial Fetch ---
    fetchAndDisplayComparison();
});