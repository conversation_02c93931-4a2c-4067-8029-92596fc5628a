<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .results { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 4px; }
        .loading { display: none; color: #007bff; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Sentiment Analysis Test</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="productName">Product Name:</label>
                <input type="text" id="productName" placeholder="Enter product name (e.g., iPhone 15)" required>
            </div>
            <button type="submit">Test Analysis</button>
            <div id="loading" class="loading">⏳ Loading...</div>
        </form>
        
        <div id="results" class="results" style="display: none;">
            <h3>Results:</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            const productName = document.getElementById('productName').value;
            
            console.log('Form submitted with product:', productName);
            
            loading.style.display = 'block';
            results.style.display = 'none';
            
            try {
                console.log('Making fetch request...');
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ product_name: productName })
                });
                
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    resultContent.innerHTML = `
                        <div class="success">✅ Success!</div>
                        <p><strong>Product:</strong> ${data.product_name}</p>
                        <p><strong>Overall Sentiment:</strong> 
                           Positive: ${data.overall_sentiment.positive}, 
                           Negative: ${data.overall_sentiment.negative}, 
                           Neutral: ${data.overall_sentiment.neutral}
                        </p>
                        <p><strong>Tweets Count:</strong> ${data.tweets_count}</p>
                        <p><strong>Sample Tweets:</strong> ${data.sample_tweets.length}</p>
                        <p><strong>Error Message:</strong> ${data.error_message || 'None'}</p>
                    `;
                    results.style.display = 'block';
                } else {
                    throw new Error(data.error || 'Unknown error');
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultContent.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                results.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        });
    </script>
</body>
</html>
