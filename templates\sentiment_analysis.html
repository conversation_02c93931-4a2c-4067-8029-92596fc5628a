<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Product Sentiment & Specs Analyzer</title>
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link href="{{ url_for('static', filename='assets/css/nucleo-icons.css') }}" rel="stylesheet" />
  <link href="{{ url_for('static', filename='assets/css/nucleo-svg.css') }}" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    .chart-container { padding: 10px; border: 1px solid #eee; border-radius: 8px; background-color: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    #aspectChartsContainer .col-md-6 { margin-bottom: 20px; }
    .product-image-container { max-width: 250px; max-height: 250px; margin: 15px auto; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa; cursor: pointer; }
    .product-image-container img { width: 100%; height: 100%; object-fit: contain; }
    #loadingSpinner { display: none; }
    .api-error-message { color: #dc3545; font-weight: bold; margin-top: 10px; font-size: 0.9em; }
    .specifications-display { background-color: #f8f9fa; border-radius: .5rem; padding: 15px; font-size: 0.9em; max-height: 250px; overflow-y: auto; border: 1px solid #e9ecef; }
    .specifications-display p, .specifications-display li { margin-bottom: 0.5rem; white-space: pre-wrap; }
    #noAspectsMessage, #noImageText, #productSpecificationsSnippet p:empty { color: #6c757d; }
    .image-zoom-modal { display: none; position: fixed; z-index: 1055; padding-top: 50px; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.8); }
    .image-zoom-modal-content { margin: auto; display: block; width: 80%; max-width: 700px; max-height: 80vh; animation-name: zoom; animation-duration: 0.4s; }
    @keyframes zoom { from {transform:scale(0)} to {transform:scale(1)} }
    .image-zoom-close { position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; transition: 0.3s; cursor: pointer; }
    .image-zoom-close:hover, .image-zoom-close:focus { color: #bbb; text-decoration: none; }

    /* ===== ENHANCED UI STYLES ===== */

    /* Enhanced Chart Containers */
    .chart-container {
      padding: 20px;
      border: none;
      border-radius: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .chart-container:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    }

    /* Enhanced Cards */
    .card {
      border: none !important;
      border-radius: 20px !important;
      box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
      transition: all 0.3s ease;
      overflow: hidden;
    }
    .card:hover {
      transform: translateY(-8px);
      box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
    }
    .card-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      color: white !important;
      border: none !important;
      padding: 20px !important;
      font-weight: 600;
    }
    .card-body {
      padding: 25px !important;
    }

    /* Enhanced Product Image Container */
    .product-image-container {
      max-width: 280px;
      max-height: 280px;
      margin: 20px auto;
      border: none;
      border-radius: 20px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      transition: transform 0.3s ease;
    }
    .product-image-container:hover {
      transform: scale(1.05);
    }
    .product-image-container img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 15px;
    }

    /* Enhanced Form Styling */
    .form-control {
      border: 2px solid #e9ecef !important;
      border-radius: 15px !important;
      padding: 15px 20px !important;
      font-size: 16px !important;
      transition: all 0.3s ease;
      background: rgba(255,255,255,0.9) !important;
    }
    .form-control:focus {
      border-color: #667eea !important;
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.3) !important;
      transform: translateY(-2px);
    }

    /* Enhanced Buttons */
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      border: none !important;
      border-radius: 25px !important;
      padding: 12px 30px !important;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    .btn-primary:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
    }

    /* Enhanced Error Messages */
    .api-error-message {
      color: #856404 !important;
      background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
      border: none !important;
      padding: 15px 20px !important;
      border-radius: 15px !important;
      box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
      font-weight: 500 !important;
    }

    /* Enhanced Loading Spinner */
    .spinner-border {
      width: 2rem;
      height: 2rem;
      border-width: 3px;
    }

    /* Enhanced List Groups */
    .list-group-item {
      border: none !important;
      border-radius: 10px !important;
      margin-bottom: 10px !important;
      padding: 15px 20px !important;
      background: rgba(255,255,255,0.8) !important;
      box-shadow: 0 3px 10px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    .list-group-item:hover {
      transform: translateX(10px);
      box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }

    /* Enhanced Background */
    .main-content {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
      min-height: 100vh;
    }

    /* Enhanced Sidebar */
    .sidenav {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
    }

    /* Enhanced Navigation */
    .navbar-main {
      background: rgba(255,255,255,0.95) !important;
      backdrop-filter: blur(10px);
      box-shadow: 0 5px 20px rgba(0,0,0,0.1) !important;
    }

    /* Animated Elements */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .card {
      animation: fadeInUp 0.6s ease-out;
    }

    /* Enhanced Typography */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 700 !important;
      color: #2d3748 !important;
    }

    /* Glassmorphism Effect */
    .glass-effect {
      background: rgba(255, 255, 255, 0.25);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.18);
    }

    /* Enhanced Aspect Charts Container */
    #aspectChartsContainer .col-md-6 {
      margin-bottom: 30px;
    }

    /* Floating Action Button */
    .fab {
      position: fixed;
      bottom: 30px;
      right: 30px;
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
      transition: all 0.3s ease;
      z-index: 1000;
      cursor: pointer;
    }
    .fab:hover {
      transform: scale(1.1) rotate(360deg);
      box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
    }

    /* Scroll to Top Button */
    .scroll-top {
      position: fixed;
      bottom: 100px;
      right: 30px;
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      border-radius: 50%;
      display: none;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
      transition: all 0.3s ease;
      z-index: 1000;
      cursor: pointer;
    }
    .scroll-top:hover {
      transform: translateY(-5px);
      box-shadow: 0 12px 35px rgba(240, 147, 251, 0.6);
    }

    /* Enhanced Progress Bar */
    .progress-bar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 10px;
      transition: all 0.3s ease;
    }

    /* Particle Background */
    .particles {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
    }

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(102, 126, 234, 0.3);
      border-radius: 50%;
      animation: float-particle 15s infinite linear;
    }

    @keyframes float-particle {
      0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
      }
      10% {
        opacity: 1;
      }
      90% {
        opacity: 1;
      }
      100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
      }
    }

    /* Enhanced Tooltips */
    .tooltip-custom {
      position: relative;
      cursor: help;
    }

    .tooltip-custom::after {
      content: attr(data-tooltip);
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 12px;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.3s ease;
      z-index: 1000;
    }

    .tooltip-custom:hover::after {
      opacity: 1;
    }

    /* Enhanced Search History */
    .search-history-item {
      background: rgba(255, 255, 255, 0.8);
      border: none;
      border-radius: 10px;
      padding: 10px 15px;
      margin: 5px 0;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .search-history-item:hover {
      background: rgba(102, 126, 234, 0.1);
      transform: translateX(5px);
    }

    /* Enhanced Modal */
    .modal-content {
      border: none;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }

    .modal-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 20px 20px 0 0;
    }
  </style>
</head>
<body class="g-sidenav-show bg-gray-100">
  <aside class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-3" id="sidenav-main">
    <div class="sidenav-header">
      <i class="fas fa-times p-3 cursor-pointer text-secondary opacity-5 position-absolute end-0 top-0 d-none d-xl-none" aria-hidden="true" id="iconSidenav"></i>
      <a class="navbar-brand m-0" href="{{ url_for('index') }}">
        <img src="{{ url_for('static', filename='assets/img/logo-ct-dark.png') }}" class="navbar-brand-img h-100" alt="main_logo">
        <span class="ms-1 font-weight-bold">Product Analyzer</span>
      </a>
    </div>
    <hr class="horizontal dark mt-0">
    <div class="collapse navbar-collapse w-auto" id="sidenav-collapse-main">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link active" href="{{ url_for('index') }}">
            <div class="icon icon-shape icon-sm shadow border-radius-md bg-white text-center me-2 d-flex align-items-center justify-content-center">
              <i class="fas fa-magnifying-glass-chart text-primary"></i>
            </div>
            <span class="nav-link-text ms-1">Analyzer</span>
          </a>
        </li>
        <li class="nav-item mt-3">
          <h6 class="ps-4 ms-2 text-uppercase text-xs font-weight-bolder opacity-6">Recent Searches</h6>
        </li>
        <div id="searchHistoryList">
            {% if history %}
                {% for item in history %}
                <li class="nav-item">
                    <a class="nav-link history-item" href="#" data-product-name="{{ item }}">
                        <div class="icon icon-shape icon-sm shadow border-radius-md bg-white text-center me-2 d-flex align-items-center justify-content-center">
                            <i class="fas fa-history text-secondary"></i>
                        </div>
                        <span class="nav-link-text ms-1">{{ item }}</span>
                    </a>
                </li>
                {% endfor %}
            {% else %}
                <li class="nav-item">
                    <span class="nav-link-text ms-4 text-xs">No recent searches.</span>
                </li>
            {% endif %}
        </div>
      </ul>
    </div>
  </aside>

  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
    <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" navbar-scroll="true">
      <div class="container-fluid py-1 px-3">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
            <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="javascript:;">Pages</a></li>
            <li class="breadcrumb-item text-sm text-dark active" aria-current="page">Analyzer</li>
          </ol>
          <h6 class="font-weight-bolder mb-0">Product Sentiment & Specs</h6>
        </nav>
        <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
          <ul class="navbar-nav justify-content-end ms-auto">
            <li class="nav-item d-flex align-items-center">
              <span class="nav-link text-body font-weight-bold px-0"><i class="fa fa-user me-sm-1"></i><span class="d-sm-inline d-none">{{ username }}</span></span>
            </li>
            <li class="nav-item d-flex align-items-center ms-3">
              <a href="{{ url_for('logout') }}" class="nav-link text-body font-weight-bold px-0"><i class="fa fa-sign-out-alt me-sm-1"></i><span class="d-sm-inline d-none">Logout</span></a>
            </li>
            <li class="nav-item d-xl-none ps-3 d-flex align-items-center">
              <a href="javascript:;" class="nav-link text-body p-0" id="iconNavbarSidenav">
                <div class="sidenav-toggler-inner"><i class="sidenav-toggler-line"></i><i class="sidenav-toggler-line"></i><i class="sidenav-toggler-line"></i></div>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
    
    <div class="container-fluid py-4">
      <div class="row">
        <div class="col-12">
          <div class="card mb-4">
            <div class="card-header pb-0"><h6>Analyze a Single Product</h6></div>
            <div class="card-body">
              <form id="sentimentForm">
                <div class="form-group">
                  <label for="productName">Product Name (e.g., iPhone 15, Samsung Galaxy S24)</label>
                  <input type="text" class="form-control" id="productName" placeholder="Enter product name" required>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Analyze Sentiment</button>
                <div id="loadingSpinner" class="spinner-border text-primary ms-3" role="status" style="display: none;">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <div id="apiErrorMessage" class="api-error-message mt-2" style="display: none;"></div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- --- NEW COMPARE PRODUCTS FORM --- -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card mb-4">
            <div class="card-header pb-0"><h6>Compare Products</h6></div>
            <div class="card-body">
              <form id="compareForm">
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="product1Name">Product 1</label>
                      <input type="text" class="form-control" id="product1Name" placeholder="e.g., iPhone 15" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="product2Name">Product 2</label>
                      <input type="text" class="form-control" id="product2Name" placeholder="e.g., Pixel 8 Pro" required>
                    </div>
                  </div>
                </div>
                <button type="submit" class="btn btn-info mt-3">Compare</button>
              </form>
            </div>
          </div>
        </div>
      </div>

      <!-- --- Results Section (for single analysis) --- -->
      <div id="resultsSection" style="display: none;">
        <h4 id="analyzedProductNameHeader" class="mt-4 mb-3 text-center"></h4>
        <div class="row">
          <div class="col-lg-4 mb-lg-0 mb-4">
            <div class="card h-100">
              <div class="card-header pb-0"><h6 class="mb-0">Product Overview</h6></div>
              <div class="card-body pt-2">
                <div id="productImageContainer" class="product-image-container" title="Click to zoom image">
                  <img id="productImage" src="#" alt="Product Image" style="display:none;">
                  <p id="noImageText" style="display:block; padding: 20px 0; color: #6c757d;">Searching for image...</p>
                </div>
                 <h6 id="tweetsCount" class="mt-3 mb-1 text-sm text-center"></h6>
                <div class="mt-3">
                  <h6 class="text-xs text-uppercase text-secondary font-weight-bolder">Specifications Snippet:</h6>
                  <div id="productSpecificationsSnippet" class="specifications-display">
                    <p style="color: #6c757d;" class="text-start">Fetching specifications...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-8">
            <div class="card h-100">
              <div class="card-header pb-0"><h6 class="mb-0">Overall Tweet Sentiment</h6></div>
              <div class="card-body p-3">
                <div class="chart-container" style="height:380px; width:100%;"><canvas id="overallSentimentChart"></canvas></div>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-12">
            <div class="card">
              <div class="card-header pb-0"><h6 class="mb-0">Aspect-Based Sentiment</h6></div>
              <div class="card-body">
                <p id="noAspectsMessage" style="display:none; color: #6c757d;">No specific aspects detected.</p>
                <div id="aspectChartsContainer" class="row"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-12">
            <div class="card">
              <div class="card-header pb-0"><h6 class="mb-0">Sample Tweets</h6></div>
              <div class="card-body"><ul id="sampleTweetsList" class="list-group"></ul></div>
            </div>
          </div>
        </div>
      </div>
      <footer class="footer pt-3">
        <!-- Footer content -->
      </footer>
    </div>

    <!-- Floating Action Button -->
    <div class="fab tooltip-custom" data-tooltip="Quick Analyze" onclick="focusSearchInput()">
      🔍
    </div>

    <!-- Scroll to Top Button -->
    <div class="scroll-top" id="scrollTopBtn" onclick="scrollToTop()">
      ↑
    </div>

    <!-- Particle Background -->
    <div class="particles" id="particles"></div>
  </main>

  <div id="imageZoomModal" class="image-zoom-modal">
    <span class="image-zoom-close">×</span>
    <img class="image-zoom-modal-content" id="zoomedImage">
  </div>
  
  <script src="{{ url_for('static', filename='assets/js/core/popper.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/core/bootstrap.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/plugins/perfect-scrollbar.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/plugins/smooth-scrollbar.min.js') }}"></script>
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="{{ url_for('static', filename='assets/js/sentiment_app.js') }}"></script></script>
  <script>
    var win = navigator.platform.indexOf('Win') > -1;
    if (win && document.querySelector('#sidenav-scrollbar')) {
      var options = { damping: '0.5' }
      Scrollbar.init(document.querySelector('#sidenav-scrollbar'), options);
    }
  </script>
  <script async defer src="https://buttons.github.io/buttons.js"></script>
  <script src="{{ url_for('static', filename='assets/js/soft-ui-dashboard.min.js') }}?v=1.1.0"></script>

  <!-- Enhanced UI JavaScript -->
  <script>
    // Floating Action Button functionality
    function focusSearchInput() {
      document.getElementById('productName').focus();
      document.getElementById('productName').scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    // Scroll to Top functionality
    function scrollToTop() {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Show/Hide Scroll to Top button
    window.addEventListener('scroll', function() {
      const scrollTopBtn = document.getElementById('scrollTopBtn');
      if (window.pageYOffset > 300) {
        scrollTopBtn.style.display = 'flex';
      } else {
        scrollTopBtn.style.display = 'none';
      }
    });

    // Create floating particles
    function createParticles() {
      const particlesContainer = document.getElementById('particles');
      const particleCount = 20;

      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        particlesContainer.appendChild(particle);
      }
    }

    // Initialize particles when page loads
    document.addEventListener('DOMContentLoaded', function() {
      createParticles();

      // Add click handlers for search history items
      document.querySelectorAll('.history-item').forEach(item => {
        item.addEventListener('click', function(e) {
          e.preventDefault();
          const productName = this.getAttribute('data-product-name');
          document.getElementById('productName').value = productName;
          focusSearchInput();
        });
      });

      // Add enhanced hover effects to cards
      document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-8px)';
        });

        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0)';
        });
      });
    });

    // Enhanced form submission with loading animation
    document.getElementById('sentimentForm').addEventListener('submit', function() {
      // Add pulse animation to the analyze button
      const btn = this.querySelector('button[type="submit"]');
      btn.style.animation = 'pulse 0.5s ease-in-out';

      setTimeout(() => {
        btn.style.animation = '';
      }, 500);
    });

    // Add pulse animation keyframes
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
    `;
    document.head.appendChild(style);
  </script>
</body>
</html>