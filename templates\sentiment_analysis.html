<!--
=========================================================
* Soft UI Dashboard 3 - v1.1.0
=========================================================
* ... (copyright header remains the same) ...
-->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="apple-touch-icon" sizes="76x76" href="{{ url_for('static', filename='assets/img/apple-icon.png') }}">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>
    Product Sentiment Analyzer
  </title>
  <!--     Fonts and icons     -->
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <!-- Nucleo Icons (Keep these if you plan to use them) -->
  <link href="{{ url_for('static', filename='assets/css/nucleo-icons.css') }}" rel="stylesheet" />
  <link href="{{ url_for('static', filename='assets/css/nucleo-svg.css') }}" rel="stylesheet" />
  
  <!-- Font Awesome Icons (REMOVED) -->
  <!-- <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script> -->

  <!-- CSS Files -->
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />
  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    /* Styles remain the same */
    .chart-container { padding: 10px; border: 1px solid #eee; border-radius: 8px; background-color: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    #aspectChartsContainer .col-md-6 { margin-bottom: 20px; }
    .product-image-container { max-width: 300px; max-height: 300px; margin: 20px auto; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; display: flex; align-items: center; justify-content: center; }
    .product-image-container img { width: 100%; height: 100%; object-fit: contain; }
    #loadingSpinner { display: none; }
    .api-error-message { color: red; font-weight: bold; margin-top: 10px; }
  </style>
</head>

<body class="g-sidenav-show  bg-gray-100">
  <aside class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-3 " id="sidenav-main">
    <div class="sidenav-header">
      <!-- Sidenav close icon (was Font Awesome, might need Nucleo replacement or removal) -->
      <i class="ni ni-fat-remove p-3 cursor-pointer text-secondary opacity-5 position-absolute end-0 top-0 d-none d-xl-none" aria-hidden="true" id="iconSidenav"></i> <!-- Example Nucleo replacement -->
      <a class="navbar-brand m-0" href="{{ url_for('index') }}">
        <img src="{{ url_for('static', filename='assets/img/logo-ct-dark.png') }}" class="navbar-brand-img h-100" alt="main_logo">
        <span class="ms-1 font-weight-bold">Sentiment Analyzer</span>
      </a>
    </div>
    <hr class="horizontal dark mt-0">
    <div class="collapse navbar-collapse  w-auto " id="sidenav-collapse-main">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link active" href="{{ url_for('index') }}">
            <div class="icon icon-shape icon-sm shadow border-radius-md bg-white text-center me-2 d-flex align-items-center justify-content-center">
              <!-- Example: Using a Nucleo icon if available, or text, or a simple SVG -->
              <i class="ni ni-shop text-primary"></i> <!-- Example Nucleo for 'shop' -->
            </div>
            <span class="nav-link-text ms-1">Analyzer</span>
          </a>
        </li>
      </ul>
    </div>
  </aside>

  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" navbar-scroll="true">
      <div class="container-fluid py-1 px-3">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
            <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="javascript:;">Pages</a></li>
            <li class="breadcrumb-item text-sm text-dark active" aria-current="page">Sentiment Analyzer</li>
          </ol>
          <h6 class="font-weight-bolder mb-0">Sentiment Analyzer</h6>
        </nav>
        <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
          <ul class="navbar-nav justify-content-end ms-auto">
            <li class="nav-item d-flex align-items-center">
              <a href="javascript:;" class="nav-link text-body font-weight-bold px-0">
                <!-- User icon (was Font Awesome) -->
                <i class="ni ni-single-02 me-sm-1"></i> <!-- Example Nucleo replacement -->
                <span class="d-sm-inline d-none">User</span>
              </a>
            </li>
             <li class="nav-item d-xl-none ps-3 d-flex align-items-center">
              <a href="javascript:;" class="nav-link text-body p-0" id="iconNavbarSidenav">
                <div class="sidenav-toggler-inner">
                  <i class="sidenav-toggler-line"></i><i class="sidenav-toggler-line"></i><i class="sidenav-toggler-line"></i>
                </div>
              </a>
            </li>
            <!-- You might have a settings cog icon here that was Font Awesome -->
            <!-- <li class="nav-item px-3 d-flex align-items-center">
              <a href="javascript:;" class="nav-link text-body p-0">
                <i class="ni ni-settings-gear-65 fixed-plugin-button-nav cursor-pointer"></i> 
              </a>
            </li> -->
            <!-- Bell icon (was Font Awesome) -->
            <!-- <li class="nav-item dropdown pe-2 d-flex align-items-center">
              <a href="javascript:;" class="nav-link text-body p-0" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="ni ni-bell-55 cursor-pointer"></i> 
              </a>
              <ul class="dropdown-menu dropdown-menu-end px-2 py-3 me-sm-n4" aria-labelledby="dropdownMenuButton">
                 ... dropdown items ...
              </ul>
            </li> -->
          </ul>
        </div>
      </div>
    </nav>

    <!-- ... rest of the HTML body ... -->
    <!-- Make sure to check the footer for any Font Awesome icons too! -->

    <div class="container-fluid py-4">
      <div class="row">
        <div class="col-12">
          <div class="card mb-4">
            <div class="card-header pb-0">
              <h6>Product Sentiment Analysis</h6>
            </div>
            <div class="card-body">
              <form id="sentimentForm">
                <div class="row">
                  <div class="col-md-12">
                    <div class="form-group">
                      <label for="productName">Product Name (e.g., iPhone 15, Samsung Galaxy S24)</label>
                      <input type="text" class="form-control" id="productName" placeholder="Enter product name" required>
                    </div>
                  </div>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Analyze Sentiment</button>
                 <div id="loadingSpinner" class="spinner-border text-primary ms-3" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <div id="apiErrorMessage" class="api-error-message mt-2" style="display: none;"></div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <div id="resultsSection" style="display: none;">
        <div class="row mt-4">
          <div class="col-md-4">
            <div class="card">
              <div class="card-header"><h5 id="analyzedProductName">Product Details</h5></div>
              <div class="card-body text-center">
                <div id="productImageContainer" class="product-image-container">
                  <img id="productImage" src="#" alt="Product Image" style="display:none;">
                  <p id="noImageText" style="display:block; padding-top: 40%;">Searching for image...</p>
                </div>
                 <h6 id="tweetsCount" class="mt-2"></h6>
              </div>
            </div>
          </div>
          <div class="col-md-8">
            <div class="card">
              <div class="card-header"><h5>Overall Sentiment</h5></div>
              <div class="card-body">
                <div class="chart-container" style="height:300px; width:100%;"><canvas id="overallSentimentChart"></canvas></div>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-12">
            <div class="card">
              <div class="card-header"><h5>Aspect-Based Sentiment Analysis</h5></div>
              <div class="card-body">
                <p id="noAspectsMessage" style="display:none;">No specific aspects detected or not enough mentions for analysis.</p>
                <div id="aspectChartsContainer" class="row"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-4">
          <div class="col-12">
            <div class="card">
              <div class="card-header"><h5>Sample Tweets Analyzed</h5></div>
              <div class="card-body"><ul id="sampleTweetsList" class="list-group"></ul></div>
            </div>
          </div>
        </div>
      </div>
      <footer class="footer pt-3  ">
        <div class="container-fluid">
          <div class="row align-items-center justify-content-lg-between">
            <div class="col-lg-6 mb-lg-0 mb-4">
              <div class="copyright text-center text-sm text-muted text-lg-start">
                © <script>document.write(new Date().getFullYear())</script>,
                made with <span style="color: #e25555;">♥</span> by <!-- Replaced FA heart -->
                <a href="https://www.creative-tim.com" class="font-weight-bold" target="_blank">Creative Tim</a>
                (Template used)
              </div>
            </div>
            <div class="col-lg-6">
              <ul class="nav nav-footer justify-content-center justify-content-lg-end">
                <li class="nav-item"><a href="https://www.creative-tim.com" class="nav-link text-muted" target="_blank">Creative Tim</a></li>
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </main>
  
  <script src="{{ url_for('static', filename='assets/js/core/popper.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/core/bootstrap.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/plugins/perfect-scrollbar.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/plugins/smooth-scrollbar.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/sentiment_app.js') }}"></script>
  <script>
    var win = navigator.platform.indexOf('Win') > -1;
    if (win && document.querySelector('#sidenav-scrollbar')) {
      var options = { damping: '0.5' }
      Scrollbar.init(document.querySelector('#sidenav-scrollbar'), options);
    }
  </script>
  <script async defer src="https://buttons.github.io/buttons.js"></script>
  <script src="{{ url_for('static', filename='assets/js/soft-ui-dashboard.min.js') }}?v=1.1.0"></script>
</body>
</html>