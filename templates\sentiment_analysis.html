<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Smartphone Sentiment Analyzer</title>

  <!-- Fonts and Icons -->
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />

  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .card-header h6 {
      font-weight: 700;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    .form-control:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    }
    .btn-primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: none;
      color: #fff;
    }
    .btn-primary:hover {
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    .nav-link.text-body {
      color: #fff !important;
    }
    .nav-link.text-body .fa {
      color: #fff !important;
    }
    .nav-link.text-body span {
      color: #fff !important;
    }
    .nav-link.active .icon {
      background: linear-gradient(135deg, #667eea, #764ba2) !important;
    }
    .nav-link.active {
      background-color: #667eea !important;
      color: #fff !important;
      border-radius: 12px;
    }
  </style>
</head>

<body class="g-sidenav-show bg-gray-100">
  <aside class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-3" id="sidenav-main">
    <div class="sidenav-header">
      <i class="fas fa-times p-3 cursor-pointer text-secondary opacity-5 position-absolute end-0 top-0 d-none d-xl-none" aria-hidden="true" id="iconSidenav"></i>
      <a class="navbar-brand m-0" href="{{ url_for('index') }}">
        <img src="{{ url_for('static', filename='assets/img/logo-ct-dark.png') }}" class="navbar-brand-img h-100" alt="main_logo">
        <span class="ms-1 font-weight-bold">Smartphone Analyzer</span>
      </a>
    </div>
    <hr class="horizontal dark mt-0">
    <div class="collapse navbar-collapse w-auto" id="sidenav-collapse-main">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link active" href="{{ url_for('index') }}">
            <div class="icon icon-shape icon-sm shadow border-radius-md text-center me-2 d-flex align-items-center justify-content-center">
              <i class="fas fa-magnifying-glass-chart text-white"></i>
            </div>
            <span class="nav-link-text ms-1">Analyzer</span>
          </a>
        </li>
        <li class="nav-item mt-3">
          <h6 class="ps-4 ms-2 text-uppercase text-xs font-weight-bolder opacity-6">Recent Searches</h6>
        </li>
        <div id="searchHistoryList">
          {% if history %}
            {% for item in history %}
            <li class="nav-item">
                <a class="nav-link history-item" href="{{ url_for('results', product1=item) }}">
                    <div class="icon icon-shape icon-sm shadow border-radius-md bg-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="fas fa-history text-secondary"></i>
                    </div>
                    <span class="nav-link-text ms-1">{{ item }}</span>
                </a>
            </li>
            {% endfor %}
          {% else %}
            <li class="nav-item">
                <span class="nav-link-text ms-4 text-xs">No recent searches.</span>
            </li>
          {% endif %}
        </div>
      </ul>
    </div>
  </aside>

  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
    <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" navbar-scroll="true">
        <div class="container-fluid py-1 px-3">
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
              <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="javascript:;">Pages</a></li>
              <li class="breadcrumb-item text-sm text-dark active" aria-current="page">Search</li>
            </ol>
            <h6 class="font-weight-bolder mb-0">Search & Compare</h6>
          </nav>
          <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
            <ul class="navbar-nav justify-content-end ms-auto">
              <li class="nav-item d-flex align-items-center">
                <span class="nav-link text-body font-weight-bold px-0">
                  <i class="fa fa-user me-sm-1"></i>
                  <span class="d-sm-inline">{{ username }}</span>
                </span>
              </li>
              <li class="nav-item d-flex align-items-center ms-3">
                <a href="{{ url_for('logout') }}" class="nav-link text-body font-weight-bold px-0">
                  <i class="fa fa-sign-out-alt me-sm-1"></i>
                  <span class="d-sm-inline">Logout</span>
                </a>
              </li>
              <li class="nav-item d-xl-none ps-3 d-flex align-items-center">
                <a href="javascript:;" class="nav-link text-body p-0" id="iconNavbarSidenav">
                  <div class="sidenav-toggler-inner">
                    <i class="sidenav-toggler-line"></i>
                    <i class="sidenav-toggler-line"></i>
                    <i class="sidenav-toggler-line"></i>
                  </div>
                </a>
              </li>
            </ul>
          </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
      <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
          <div class="card mb-4">
            <div class="card-header pb-0">
              <h6>Analyze and Compare Smartphones</h6>
            </div>
            <div class="card-body">
              {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                  {% for category, message in messages %}
                    <div class="alert alert-{{ category }} text-white" role="alert">{{ message }}</div>
                  {% endfor %}
                {% endif %}
              {% endwith %}

              <form id="sentimentForm" method="GET" action="{{ url_for('results') }}">
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="productName1">Smartphone 1 Name</label>
                      <input type="text" class="form-control" id="productName1" name="product1" placeholder="e.g., iPhone 15" required>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="productName2">Smartphone 2 Name (Optional - for comparison)</label>
                      <input type="text" class="form-control" id="productName2" name="product2" placeholder="e.g., Samsung Galaxy S24">
                    </div>
                  </div>
                </div>
                <div class="text-center">
                  <button type="submit" class="btn btn-primary mt-4">Analyze</button>
                </div>
              </form>

            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <script src="{{ url_for('static', filename='assets/js/core/popper.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/core/bootstrap.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/soft-ui-dashboard.min.js') }}?v=1.1.0"></script>
</body>
</html>

