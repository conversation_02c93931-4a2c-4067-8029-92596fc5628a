import nltk
from nltk.sentiment.vader import SentimentIntensityAnalyzer
from nltk.tokenize import sent_tokenize
import random
import tweepy
import os
import logging
from googleapiclient.discovery import build # For Google Image Search

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# NLTK resources
try:
    sid = SentimentIntensityAnalyzer()
except LookupError:
    nltk.download('vader_lexicon', quiet=True)
    sid = SentimentIntensityAnalyzer()
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt', quiet=True)

# Aspect Keywords (same as before)
ASPECTS_KEYWORDS = {
    "battery": ["battery", "power", "charge", "life", "charging", "mah", "backup"],
    "camera": ["camera", "photo", "picture", "lens", "image", "video", "shot", "sensor", "zoom", "pixel", "focus", "megapixel", "mp"],
    "screen": ["screen", "display", "resolution", "amoled", "lcd", "brightness", "hdr", "refresh rate", "size", "oled", "gorilla glass"],
    "performance": ["performance", "speed", "fast", "slow", "lag", "chip", "processor", "ram", "gaming", "smooth", "snapdragon", "bionic"],
    "price": ["price", "cost", "value", "cheap", "expensive", "budget", "affordable", "money", "worth"],
    "design": ["design", "look", "feel", "build", "aesthetic", "style", "beautiful", "ugly", "premium", "color", "material"],
    "software": ["software", "os", "ui", "update", "app", "bloatware", "interface", "android", "ios", "oxygenos", "miui", "oneui"],
    "sound": ["sound", "audio", "speaker", "music", "volume", "microphone", "call quality", "dolby"],
    "durability": ["durability", "strong", "robust", "scratch", "waterproof", "resistant", "ip rating", "ip68"],
    "features": ["feature", "functionality", "fingerprint", "face id", "nfc", "5g", "wireless charging", "storage"],
    "connectivity": ["connectivity", "wifi", "bluetooth", "network", "signal", "5g", "lte"],
    "support": ["support", "customer service", "warranty", "repair"]
}

def fetch_product_image_url(product_name):
    api_key = os.getenv("GOOGLE_API_KEY")
    cse_id = os.getenv("GOOGLE_CSE_ID")

    if not api_key or not cse_id:
        logger.warning("Google API Key or CSE ID not configured for image search.")
        return None

    try:
        service = build("customsearch", "v1", developerKey=api_key)
        res = service.cse().list(
            q=f"{product_name} product image", # Query for product image
            cx=cse_id,
            searchType='image',
            num=1, # Get the first image
            imgSize='MEDIUM', # Request medium sized images (correct parameter value)
            safe='medium'
        ).execute()

        if 'items' in res and len(res['items']) > 0:
            # Prioritize cse_image if available, otherwise use link
            if 'pagemap' in res['items'][0] and 'cse_image' in res['items'][0]['pagemap'] and len(res['items'][0]['pagemap']['cse_image']) > 0:
                 image_url = res['items'][0]['pagemap']['cse_image'][0]['src']
                 logger.info(f"Found image via cse_image: {image_url}")
                 return image_url
            image_url = res['items'][0]['link']
            logger.info(f"Found image via link: {image_url}")
            return image_url
        else:
            logger.info(f"No image found for {product_name}")
            return None
    except Exception as e:
        logger.error(f"Error fetching product image for '{product_name}': {e}")
        return None


def fetch_real_tweets(product_name, count=50):
    bearer_token = os.getenv("TWITTER_BEARER_TOKEN")
    if not bearer_token:
        logger.error("TWITTER_BEARER_TOKEN is not set.")
        return [], "Twitter API Bearer Token not configured."
    try:
        client = tweepy.Client(bearer_token)
        query = f'"{product_name}" -is:retweet lang:en'
        fetch_count = max(10, min(count, 100))
        response = client.search_recent_tweets(query, tweet_fields=['text'], max_results=fetch_count)
        tweets_data = [tweet.text for tweet in response.data] if response.data else []
        logger.info(f"Fetched {len(tweets_data)} tweets for '{product_name}'.")
        return tweets_data, None
    except tweepy.TweepyException as e:
        logger.error(f"Tweepy API error: {e}")
        return [], f"Twitter API error: {str(e)[:100]}..." # Truncate long error messages
    except Exception as e:
        logger.error(f"Unexpected error fetching tweets: {e}")
        return [], f"Unexpected error fetching tweets: {str(e)[:100]}..."

def analyze_sentiment_vader(text):
    scores = sid.polarity_scores(text)
    compound = scores['compound']
    if compound >= 0.05: return 'positive', scores
    elif compound <= -0.05: return 'negative', scores
    else: return 'neutral', scores

def get_product_sentiment_analysis(product_name):
    tweets, twitter_error_message = fetch_real_tweets(product_name, count=100)
    product_image = fetch_product_image_url(product_name)
    
    # Initialize results structure
    results = {
        'product_image_url': product_image,
        'overall_sentiment': {'positive': 0, 'negative': 0, 'neutral': 0},
        'aspect_sentiments': {},
        'tweets_count': 0,
        'sample_tweets': [],
        'error_message': twitter_error_message
    }

    if not tweets: # No tweets available (either API error or no tweets found)
        # Provide demo data when Twitter API is not available or fails
        logger.info("Using demo data since no tweets were fetched")
        results['overall_sentiment'] = {'positive': 15, 'negative': 5, 'neutral': 10}
        results['tweets_count'] = 30
        results['aspect_sentiments'] = {
            'camera': {'positive': 8, 'negative': 2, 'neutral': 3, 'mentions': 13},
            'battery': {'positive': 5, 'negative': 3, 'neutral': 2, 'mentions': 10},
            'performance': {'positive': 7, 'negative': 1, 'neutral': 4, 'mentions': 12},
            'price': {'positive': 3, 'negative': 4, 'neutral': 3, 'mentions': 10}
        }
        results['sample_tweets'] = [
            {"text": f"Just got the {product_name} and the camera quality is amazing! 📸", "sentiment": "positive"},
            {"text": f"The {product_name} battery lasts all day, very impressed", "sentiment": "positive"},
            {"text": f"{product_name} performance is smooth, no lag at all", "sentiment": "positive"},
            {"text": f"Not sure about the {product_name} price, seems a bit expensive", "sentiment": "negative"},
            {"text": f"The {product_name} is okay, nothing special but does the job", "sentiment": "neutral"}
        ]
        if twitter_error_message:
            results['error_message'] = f"Demo data shown. {twitter_error_message}"
        else:
            results['error_message'] = "Demo data shown. No tweets found."
        return results

    # Proceed with analysis if tweets were fetched
    results['tweets_count'] = len(tweets)
    aspect_sentiments_data = {aspect: {'positive': 0, 'negative': 0, 'neutral': 0, 'mentions': 0} for aspect in ASPECTS_KEYWORDS}
    analyzed_tweets_details = []

    for tweet_text in tweets:
        sentiment_label, _ = analyze_sentiment_vader(tweet_text)
        results['overall_sentiment'][sentiment_label] += 1
        analyzed_tweets_details.append({'text': tweet_text, 'sentiment': sentiment_label})

        sentences = sent_tokenize(tweet_text.lower())
        tweet_aspects_found = set()
        for sentence in sentences:
            sentence_sentiment_label, _ = analyze_sentiment_vader(sentence)
            for aspect, keywords in ASPECTS_KEYWORDS.items():
                if any(keyword in sentence for keyword in keywords):
                    if aspect not in tweet_aspects_found:
                        aspect_sentiments_data[aspect]['mentions'] += 1
                        tweet_aspects_found.add(aspect)
                    aspect_sentiments_data[aspect][sentence_sentiment_label] += 1
    
    min_mentions_for_chart = 1
    results['aspect_sentiments'] = {
        asp: data for asp, data in aspect_sentiments_data.items() if data['mentions'] >= min_mentions_for_chart
    }
    results['sample_tweets'] = random.sample(analyzed_tweets_details, min(5, len(analyzed_tweets_details)))
    
    return results