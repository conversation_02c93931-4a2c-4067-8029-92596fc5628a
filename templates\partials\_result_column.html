{% if results.error_message and not results.tweets_count > 0 %}
  <div class="alert alert-warning text-white">{{ results.error_message }}</div>
{% endif %}
<h4 class="mt-2 mb-3 text-center">{{ product_name }}</h4>
<div class="row">
    <!-- Product Overview Card -->
    <div class="col-12 mb-4">
        <div class="card h-100">
          <div class="card-header pb-0"><h6 class="mb-0">Product Overview</h6></div>
          <div class="card-body pt-2">
            <div id="productImageContainer{{ id_suffix }}" class="product-image-container" title="Click to zoom image">
              {% if results.product_image_url %}
                <img id="productImage{{ id_suffix }}" src="{{ results.product_image_url }}" alt="Product Image {{ product_name }}">
              {% else %}
                <p id="noImageText{{ id_suffix }}" style="padding: 20px 0; color: #6c757d;">No product image found.</p>
              {% endif %}
            </div>
            <h6 class="mt-3 mb-1 text-sm text-center">Based on {{ results.tweets_count }} tweets.</h6>
            <div class="mt-3">
              <h6 class="text-xs text-uppercase text-secondary font-weight-bolder">Specifications Snippet:</h6>
              <div id="productSpecificationsSnippet{{ id_suffix }}" class="specifications-display"></div>
            </div>
          </div>
        </div>
    </div>
    <!-- Overall Sentiment Card -->
    <div class="col-12 mb-4">
        <div class="card h-100">
            <div class="card-header pb-0"><h6 class="mb-0">Overall Tweet Sentiment</h6></div>
            <div class="card-body p-3">
                <div class="chart-container" style="height:300px; width:100%;"><canvas id="overallSentimentChart{{ id_suffix }}"></canvas></div>
            </div>
        </div>
    </div>
    <!-- Aspect-Based Sentiment Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header pb-0"><h6 class="mb-0">Aspect-Based Sentiment</h6></div>
            <div class="card-body">
                <p id="noAspectsMessage{{ id_suffix }}" style="display: {% if not results.aspect_sentiments %}block{% else %}none{% endif %}; color: #6c757d;">No aspects detected.</p>
                <div id="aspectChartsContainer{{ id_suffix }}" class="row"></div>
            </div>
        </div>
    </div>
    <!-- Sample Tweets Card -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header pb-0"><h6 class="mb-0">Sample Tweets</h6></div>
            <div class="card-body">
                <ul class="list-group">
                  {% for tweet in results.sample_tweets %}
                    <li class="list-group-item">
                      {% if tweet.sentiment == 'positive' %}<span class="fw-bold text-success">[POSITIVE]</span>
                      {% elif tweet.sentiment == 'negative' %}<span class="fw-bold text-danger">[NEGATIVE]</span>
                      {% else %}<span class="fw-bold text-secondary">[NEUTRAL]</span>
                      {% endif %}: {{ tweet.text }}
                    </li>
                  {% else %}
                    <li class="list-group-item">No sample tweets available.</li>
                  {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>