{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://cloudasset.googleapis.com/", "batchPath": "batch", "canonicalName": "Cloud Asset", "description": "The Cloud Asset API manages the history and inventory of Google Cloud resources.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/asset-inventory/docs/quickstart", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "cloudasset:v1p1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://cloudasset.mtls.googleapis.com/", "name": "cloudasset", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"iamPolicies": {"methods": {"searchAll": {"description": "Searches all the IAM policies within a given accessible Resource Manager scope (project/folder/organization). This RPC gives callers especially administrators the ability to search all the IAM policies within a scope, even if they don't have `.getIamPolicy` permission of all the IAM policies. Callers should have `cloudasset.assets.searchAllIamPolicies` permission on the requested scope, otherwise the request will be rejected.", "flatPath": "v1p1beta1/{v1p1beta1Id}/{v1p1beta1Id1}/iamPolicies:searchAll", "httpMethod": "GET", "id": "cloudasset.iamPolicies.searchAll", "parameterOrder": ["scope"], "parameters": {"pageSize": {"description": "Optional. The page size for search result pagination. Page size is capped at 500 even if a larger value is given. If set to zero, server will pick an appropriate default. Returned results may be fewer than requested. When this happens, there could be more results as long as `next_page_token` is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. If present, retrieve the next batch of results from the preceding call to this method. `page_token` must be the value of `next_page_token` from the previous response. The values of all other method parameters must be identical to those in the previous call.", "location": "query", "type": "string"}, "query": {"description": "Optional. The query statement. Examples: * \"policy:<EMAIL>\" * \"policy:(<EMAIL> viewer)\"", "location": "query", "type": "string"}, "scope": {"description": "Required. The relative name of an asset. The search is limited to the resources within the `scope`. The allowed value must be: * Organization number (such as \"organizations/123\") * Folder number (such as \"folders/1234\") * Project number (such as \"projects/12345\") * Project ID (such as \"projects/abc\")", "location": "path", "pattern": "^[^/]+/[^/]+$", "required": true, "type": "string"}}, "path": "v1p1beta1/{+scope}/iamPolicies:searchAll", "response": {"$ref": "SearchAllIamPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "resources": {"methods": {"searchAll": {"description": "Searches all the resources within a given accessible Resource Manager scope (project/folder/organization). This RPC gives callers especially administrators the ability to search all the resources within a scope, even if they don't have `.get` permission of all the resources. Callers should have `cloudasset.assets.searchAllResources` permission on the requested scope, otherwise the request will be rejected.", "flatPath": "v1p1beta1/{v1p1beta1Id}/{v1p1beta1Id1}/resources:searchAll", "httpMethod": "GET", "id": "cloudasset.resources.searchAll", "parameterOrder": ["scope"], "parameters": {"assetTypes": {"description": "Optional. A list of asset types that this request searches for. If empty, it will search all the supported asset types.", "location": "query", "repeated": true, "type": "string"}, "orderBy": {"description": "Optional. A comma separated list of fields specifying the sorting order of the results. The default order is ascending. Add ` DESC` after the field name to indicate descending order. Redundant space characters are ignored. For example, ` location DESC , name `.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The page size for search result pagination. Page size is capped at 500 even if a larger value is given. If set to zero, server will pick an appropriate default. Returned results may be fewer than requested. When this happens, there could be more results as long as `next_page_token` is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. If present, then retrieve the next batch of results from the preceding call to this method. `page_token` must be the value of `next_page_token` from the previous response. The values of all other method parameters, must be identical to those in the previous call.", "location": "query", "type": "string"}, "query": {"description": "Optional. The query statement.", "location": "query", "type": "string"}, "scope": {"description": "Required. The relative name of an asset. The search is limited to the resources within the `scope`. The allowed value must be: * Organization number (such as \"organizations/123\") * Folder number (such as \"folders/1234\") * Project number (such as \"projects/12345\") * Project ID (such as \"projects/abc\")", "location": "path", "pattern": "^[^/]+/[^/]+$", "required": true, "type": "string"}}, "path": "v1p1beta1/{+scope}/resources:searchAll", "response": {"$ref": "SearchAllResourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20250307", "rootUrl": "https://cloudasset.googleapis.com/", "schemas": {"AnalyzeIamPolicyLongrunningMetadata": {"description": "Represents the metadata of the longrunning operation for the AnalyzeIamPolicyLongrunning RPC.", "id": "AnalyzeIamPolicyLongrunningMetadata", "properties": {"createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "AnalyzeIamPolicyLongrunningResponse": {"description": "A response message for AssetService.AnalyzeIamPolicyLongrunning.", "id": "AnalyzeIamPolicyLongrunningResponse", "properties": {}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "Explanation": {"description": "Explanation about the IAM policy search result.", "id": "Explanation", "properties": {"matchedPermissions": {"additionalProperties": {"$ref": "Permissions"}, "description": "The map from roles to their included permission matching the permission query (e.g. containing `policy.role.permissions:`). Example role string: \"roles/compute.instanceAdmin\". The roles can also be found in the returned `policy` bindings. Note that the map is populated only if requesting with a permission query.", "type": "object"}}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GoogleCloudAssetV1p7beta1Asset": {"description": "An asset in Google Cloud. An asset can be any resource in the Google Cloud [resource hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy), a resource outside the Google Cloud resource hierarchy (such as Google Kubernetes Engine clusters and objects), or a policy (e.g. IAM policy). See [Supported asset types](https://cloud.google.com/asset-inventory/docs/supported-asset-types) for more information.", "id": "GoogleCloudAssetV1p7beta1Asset", "properties": {"accessLevel": {"$ref": "GoogleIdentityAccesscontextmanagerV1AccessLevel", "description": "Please also refer to the [access level user guide](https://cloud.google.com/access-context-manager/docs/overview#access-levels)."}, "accessPolicy": {"$ref": "GoogleIdentityAccesscontextmanagerV1AccessPolicy", "description": "Please also refer to the [access policy user guide](https://cloud.google.com/access-context-manager/docs/overview#access-policies)."}, "ancestors": {"description": "The ancestry path of an asset in Google Cloud [resource hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy), represented as a list of relative resource names. An ancestry path starts with the closest ancestor in the hierarchy and ends at root. If the asset is a project, folder, or organization, the ancestry path starts from the asset itself. Example: `[\"projects/123456789\", \"folders/5432\", \"organizations/1234\"]`", "items": {"type": "string"}, "type": "array"}, "assetType": {"description": "The type of the asset. Example: `compute.googleapis.com/Disk` See [Supported asset types](https://cloud.google.com/asset-inventory/docs/supported-asset-types) for more information.", "type": "string"}, "iamPolicy": {"$ref": "Policy", "description": "A representation of the IAM policy set on a Google Cloud resource. There can be a maximum of one IAM policy set on any given resource. In addition, IAM policies inherit their granted access scope from any policies set on parent resources in the resource hierarchy. Therefore, the effectively policy is the union of both the policy set on this resource and each policy set on all of the resource's ancestry resource levels in the hierarchy. See [this topic](https://cloud.google.com/iam/help/allow-policies/inheritance) for more information."}, "name": {"description": "The full name of the asset. Example: `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1` See [Resource names](https://cloud.google.com/apis/design/resource_names#full_resource_name) for more information.", "type": "string"}, "orgPolicy": {"description": "A representation of an [organization policy](https://cloud.google.com/resource-manager/docs/organization-policy/overview#organization_policy). There can be more than one organization policy with different constraints set on a given resource.", "items": {"$ref": "GoogleCloudOrgpolicyV1Policy"}, "type": "array"}, "relatedAssets": {"$ref": "GoogleCloudAssetV1p7beta1RelatedAssets", "description": "The related assets of the asset of one relationship type. One asset only represents one type of relationship."}, "resource": {"$ref": "GoogleCloudAssetV1p7beta1Resource", "description": "A representation of the resource."}, "servicePerimeter": {"$ref": "GoogleIdentityAccesscontextmanagerV1ServicePerimeter", "description": "Please also refer to the [service perimeter user guide](https://cloud.google.com/vpc-service-controls/docs/overview)."}, "updateTime": {"description": "The last update timestamp of an asset. update_time is updated when create/update/delete operation is performed.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudAssetV1p7beta1RelatedAsset": {"description": "An asset identify in Google Cloud which contains its name, type and ancestors. An asset can be any resource in the Google Cloud [resource hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy), a resource outside the Google Cloud resource hierarchy (such as Google Kubernetes Engine clusters and objects), or a policy (e.g. IAM policy). See [Supported asset types](https://cloud.google.com/asset-inventory/docs/supported-asset-types) for more information.", "id": "GoogleCloudAssetV1p7beta1RelatedAsset", "properties": {"ancestors": {"description": "The ancestors of an asset in Google Cloud [resource hierarchy](https://cloud.google.com/resource-manager/docs/cloud-platform-resource-hierarchy), represented as a list of relative resource names. An ancestry path starts with the closest ancestor in the hierarchy and ends at root. Example: `[\"projects/123456789\", \"folders/5432\", \"organizations/1234\"]`", "items": {"type": "string"}, "type": "array"}, "asset": {"description": "The full name of the asset. Example: `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1` See [Resource names](https://cloud.google.com/apis/design/resource_names#full_resource_name) for more information.", "type": "string"}, "assetType": {"description": "The type of the asset. Example: `compute.googleapis.com/Disk` See [Supported asset types](https://cloud.google.com/asset-inventory/docs/supported-asset-types) for more information.", "type": "string"}}, "type": "object"}, "GoogleCloudAssetV1p7beta1RelatedAssets": {"description": "The detailed related assets with the `relationship_type`.", "id": "GoogleCloudAssetV1p7beta1RelatedAssets", "properties": {"assets": {"description": "The peer resources of the relationship.", "items": {"$ref": "GoogleCloudAssetV1p7beta1RelatedAsset"}, "type": "array"}, "relationshipAttributes": {"$ref": "GoogleCloudAssetV1p7beta1RelationshipAttributes", "description": "The detailed relation attributes."}}, "type": "object"}, "GoogleCloudAssetV1p7beta1RelationshipAttributes": {"description": "The relationship attributes which include `type`, `source_resource_type`, `target_resource_type` and `action`.", "id": "GoogleCloudAssetV1p7beta1RelationshipAttributes", "properties": {"action": {"description": "The detail of the relationship, e.g. `contains`, `attaches`", "type": "string"}, "sourceResourceType": {"description": "The source asset type. Example: `compute.googleapis.com/Instance`", "type": "string"}, "targetResourceType": {"description": "The target asset type. Example: `compute.googleapis.com/Disk`", "type": "string"}, "type": {"description": "The unique identifier of the relationship type. Example: `INSTANCE_TO_INSTANCEGROUP`", "type": "string"}}, "type": "object"}, "GoogleCloudAssetV1p7beta1Resource": {"description": "A representation of a Google Cloud resource.", "id": "GoogleCloudAssetV1p7beta1Resource", "properties": {"data": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "The content of the resource, in which some sensitive fields are removed and may not be present.", "type": "object"}, "discoveryDocumentUri": {"description": "The URL of the discovery document containing the resource's JSON schema. Example: `https://www.googleapis.com/discovery/v1/apis/compute/v1/rest` This value is unspecified for resources that do not have an API based on a discovery document, such as Cloud Bigtable.", "type": "string"}, "discoveryName": {"description": "The JSON schema name listed in the discovery document. Example: `Project` This value is unspecified for resources that do not have an API based on a discovery document, such as Cloud Bigtable.", "type": "string"}, "location": {"description": "The location of the resource in Google Cloud, such as its zone and region. For more information, see https://cloud.google.com/about/locations/.", "type": "string"}, "parent": {"description": "The full name of the immediate parent of this resource. See [Resource Names](https://cloud.google.com/apis/design/resource_names#full_resource_name) for more information. For Google Cloud assets, this value is the parent resource defined in the [IAM policy hierarchy](https://cloud.google.com/iam/docs/overview#policy_hierarchy). Example: `//cloudresourcemanager.googleapis.com/projects/my_project_123` For third-party assets, this field may be set differently.", "type": "string"}, "resourceUrl": {"description": "The REST URL for accessing the resource. An HTTP `GET` request using this URL returns the resource itself. Example: `https://cloudresourcemanager.googleapis.com/v1/projects/my-project-123` This value is unspecified for resources without a REST API.", "type": "string"}, "version": {"description": "The API version. Example: `v1`", "type": "string"}}, "type": "object"}, "GoogleCloudOrgpolicyV1BooleanPolicy": {"description": "Used in `policy_type` to specify how `boolean_policy` will behave at this resource.", "id": "GoogleCloudOrgpolicyV1BooleanPolicy", "properties": {"enforced": {"description": "If `true`, then the `Policy` is enforced. If `false`, then any configuration is acceptable. Suppose you have a `Constraint` `constraints/compute.disableSerialPortAccess` with `constraint_default` set to `ALLOW`. A `Policy` for that `Constraint` exhibits the following behavior: - If the `Policy` at this resource has enforced set to `false`, serial port connection attempts will be allowed. - If the `Policy` at this resource has enforced set to `true`, serial port connection attempts will be refused. - If the `Policy` at this resource is `RestoreDefault`, serial port connection attempts will be allowed. - If no `Policy` is set at this resource or anywhere higher in the resource hierarchy, serial port connection attempts will be allowed. - If no `Policy` is set at this resource, but one exists higher in the resource hierarchy, the behavior is as if the`Policy` were set at this resource. The following examples demonstrate the different possible layerings: Example 1 (nearest `Constraint` wins): `organizations/foo` has a `Policy` with: {enforced: false} `projects/bar` has no `Policy` set. The constraint at `projects/bar` and `organizations/foo` will not be enforced. Example 2 (enforcement gets replaced): `organizations/foo` has a `Policy` with: {enforced: false} `projects/bar` has a `Policy` with: {enforced: true} The constraint at `organizations/foo` is not enforced. The constraint at `projects/bar` is enforced. Example 3 (RestoreDefault): `organizations/foo` has a `Policy` with: {enforced: true} `projects/bar` has a `Policy` with: {RestoreDefault: {}} The constraint at `organizations/foo` is enforced. The constraint at `projects/bar` is not enforced, because `constraint_default` for the `Constraint` is `ALLOW`.", "type": "boolean"}}, "type": "object"}, "GoogleCloudOrgpolicyV1ListPolicy": {"description": "Used in `policy_type` to specify how `list_policy` behaves at this resource. `ListPolicy` can define specific values and subtrees of Cloud Resource Manager resource hierarchy (`Organizations`, `Folders`, `Projects`) that are allowed or denied by setting the `allowed_values` and `denied_values` fields. This is achieved by using the `under:` and optional `is:` prefixes. The `under:` prefix is used to denote resource subtree values. The `is:` prefix is used to denote specific values, and is required only if the value contains a \":\". Values prefixed with \"is:\" are treated the same as values with no prefix. Ancestry subtrees must be in one of the following formats: - \"projects/\", e.g. \"projects/tokyo-rain-123\" - \"folders/\", e.g. \"folders/1234\" - \"organizations/\", e.g. \"organizations/1234\" The `supports_under` field of the associated `Constraint` defines whether ancestry prefixes can be used. You can set `allowed_values` and `denied_values` in the same `Policy` if `all_values` is `ALL_VALUES_UNSPECIFIED`. `ALLOW` or `DENY` are used to allow or deny all values. If `all_values` is set to either `ALLOW` or `DENY`, `allowed_values` and `denied_values` must be unset.", "id": "GoogleCloudOrgpolicyV1ListPolicy", "properties": {"allValues": {"description": "The policy all_values state.", "enum": ["ALL_VALUES_UNSPECIFIED", "ALLOW", "DENY"], "enumDescriptions": ["Indicates that allowed_values or denied_values must be set.", "A policy with this set allows all values.", "A policy with this set denies all values."], "type": "string"}, "allowedValues": {"description": "List of values allowed at this resource. Can only be set if `all_values` is set to `ALL_VALUES_UNSPECIFIED`.", "items": {"type": "string"}, "type": "array"}, "deniedValues": {"description": "List of values denied at this resource. Can only be set if `all_values` is set to `ALL_VALUES_UNSPECIFIED`.", "items": {"type": "string"}, "type": "array"}, "inheritFromParent": {"description": "Determines the inheritance behavior for this `Policy`. By default, a `ListPolicy` set at a resource supersedes any `Policy` set anywhere up the resource hierarchy. However, if `inherit_from_parent` is set to `true`, then the values from the effective `Policy` of the parent resource are inherited, meaning the values set in this `Policy` are added to the values inherited up the hierarchy. Setting `Policy` hierarchies that inherit both allowed values and denied values isn't recommended in most circumstances to keep the configuration simple and understandable. However, it is possible to set a `Policy` with `allowed_values` set that inherits a `Policy` with `denied_values` set. In this case, the values that are allowed must be in `allowed_values` and not present in `denied_values`. For example, suppose you have a `Constraint` `constraints/serviceuser.services`, which has a `constraint_type` of `list_constraint`, and with `constraint_default` set to `ALLOW`. Suppose that at the Organization level, a `Policy` is applied that restricts the allowed API activations to {`E1`, `E2`}. Then, if a `Policy` is applied to a project below the Organization that has `inherit_from_parent` set to `false` and field all_values set to DENY, then an attempt to activate any API will be denied. The following examples demonstrate different possible layerings for `projects/bar` parented by `organizations/foo`: Example 1 (no inherited values): `organizations/foo` has a `Policy` with values: {allowed_values: \"E1\" allowed_values:\"E2\"} `projects/bar` has `inherit_from_parent` `false` and values: {allowed_values: \"E3\" allowed_values: \"E4\"} The accepted values at `organizations/foo` are `E1`, `E2`. The accepted values at `projects/bar` are `E3`, and `E4`. Example 2 (inherited values): `organizations/foo` has a `Policy` with values: {allowed_values: \"E1\" allowed_values:\"E2\"} `projects/bar` has a `Policy` with values: {value: \"E3\" value: \"E4\" inherit_from_parent: true} The accepted values at `organizations/foo` are `E1`, `E2`. The accepted values at `projects/bar` are `E1`, `E2`, `E3`, and `E4`. Example 3 (inheriting both allowed and denied values): `organizations/foo` has a `Policy` with values: {allowed_values: \"E1\" allowed_values: \"E2\"} `projects/bar` has a `Policy` with: {denied_values: \"E1\"} The accepted values at `organizations/foo` are `E1`, `E2`. The value accepted at `projects/bar` is `E2`. Example 4 (RestoreDefault): `organizations/foo` has a `Policy` with values: {allowed_values: \"E1\" allowed_values:\"E2\"} `projects/bar` has a `Policy` with values: {RestoreDefault: {}} The accepted values at `organizations/foo` are `E1`, `E2`. The accepted values at `projects/bar` are either all or none depending on the value of `constraint_default` (if `ALLOW`, all; if `DENY`, none). Example 5 (no policy inherits parent policy): `organizations/foo` has no `Policy` set. `projects/bar` has no `Policy` set. The accepted values at both levels are either all or none depending on the value of `constraint_default` (if `ALLOW`, all; if `DENY`, none). Example 6 (ListConstraint allowing all): `organizations/foo` has a `Policy` with values: {allowed_values: \"E1\" allowed_values: \"E2\"} `projects/bar` has a `Policy` with: {all: ALLOW} The accepted values at `organizations/foo` are `E1`, E2`. Any value is accepted at `projects/bar`. Example 7 (ListConstraint allowing none): `organizations/foo` has a `Policy` with values: {allowed_values: \"E1\" allowed_values: \"E2\"} `projects/bar` has a `Policy` with: {all: DENY} The accepted values at `organizations/foo` are `E1`, E2`. No value is accepted at `projects/bar`. Example 10 (allowed and denied subtrees of Resource Manager hierarchy): Given the following resource hierarchy O1->{F1, F2}; F1->{P1}; F2->{P2, P3}, `organizations/foo` has a `Policy` with values: {allowed_values: \"under:organizations/O1\"} `projects/bar` has a `Policy` with: {allowed_values: \"under:projects/P3\"} {denied_values: \"under:folders/F2\"} The accepted values at `organizations/foo` are `organizations/O1`, `folders/F1`, `folders/F2`, `projects/P1`, `projects/P2`, `projects/P3`. The accepted values at `projects/bar` are `organizations/O1`, `folders/F1`, `projects/P1`.", "type": "boolean"}, "suggestedValue": {"description": "Optional. The Google Cloud Console will try to default to a configuration that matches the value specified in this `Policy`. If `suggested_value` is not set, it will inherit the value specified higher in the hierarchy, unless `inherit_from_parent` is `false`.", "type": "string"}}, "type": "object"}, "GoogleCloudOrgpolicyV1Policy": {"description": "Defines a Cloud Organization `Policy` which is used to specify `Constraints` for configurations of Cloud Platform resources.", "id": "GoogleCloudOrgpolicyV1Policy", "properties": {"booleanPolicy": {"$ref": "GoogleCloudOrgpolicyV1BooleanPolicy", "description": "For boolean `Constraints`, whether to enforce the `Constraint` or not."}, "constraint": {"description": "The name of the `Constraint` the `Policy` is configuring, for example, `constraints/serviceuser.services`. A [list of available constraints](/resource-manager/docs/organization-policy/org-policy-constraints) is available. Immutable after creation.", "type": "string"}, "etag": {"description": "An opaque tag indicating the current version of the `Policy`, used for concurrency control. When the `Policy` is returned from either a `GetPolicy` or a `ListOrgPolicy` request, this `etag` indicates the version of the current `Policy` to use when executing a read-modify-write loop. When the `Policy` is returned from a `GetEffectivePolicy` request, the `etag` will be unset. When the `Policy` is used in a `SetOrgPolicy` method, use the `etag` value that was returned from a `GetOrgPolicy` request as part of a read-modify-write loop for concurrency control. Not setting the `etag`in a `SetOrgPolicy` request will result in an unconditional write of the `Policy`.", "format": "byte", "type": "string"}, "listPolicy": {"$ref": "GoogleCloudOrgpolicyV1ListPolicy", "description": "List of values either allowed or disallowed."}, "restoreDefault": {"$ref": "GoogleCloudOrgpolicyV1RestoreDefault", "description": "Restores the default behavior of the constraint; independent of `Constraint` type."}, "updateTime": {"description": "The time stamp the `Policy` was previously updated. This is set by the server, not specified by the caller, and represents the last time a call to `SetOrgPolicy` was made for that `Policy`. Any value set by the client will be ignored.", "format": "google-datetime", "type": "string"}, "version": {"description": "Version of the `Policy`. Default version is 0;", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudOrgpolicyV1RestoreDefault": {"description": "Ignores policies set above this resource and restores the `constraint_default` enforcement behavior of the specific `Constraint` at this resource. Suppose that `constraint_default` is set to `ALLOW` for the `Constraint` `constraints/serviceuser.services`. Suppose that organization foo.com sets a `Policy` at their Organization resource node that restricts the allowed service activations to deny all service activations. They could then set a `Policy` with the `policy_type` `restore_default` on several experimental projects, restoring the `constraint_default` enforcement of the `Constraint` for only those projects, allowing those projects to have all services activated.", "id": "GoogleCloudOrgpolicyV1RestoreDefault", "properties": {}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1AccessLevel": {"description": "An `AccessLevel` is a label that can be applied to requests to Google Cloud services, along with a list of requirements necessary for the label to be applied.", "id": "GoogleIdentityAccesscontextmanagerV1AccessLevel", "properties": {"basic": {"$ref": "GoogleIdentityAccesscontextmanagerV1BasicLevel", "description": "A `BasicLevel` composed of `Conditions`."}, "custom": {"$ref": "GoogleIdentityAccesscontextmanagerV1CustomLevel", "description": "A `CustomLevel` written in the Common Expression Language."}, "description": {"description": "Description of the `AccessLevel` and its use. Does not affect behavior.", "type": "string"}, "name": {"description": "Identifier. Resource name for the `AccessLevel`. Format: `accessPolicies/{access_policy}/accessLevels/{access_level}`. The `access_level` component must begin with a letter, followed by alphanumeric characters or `_`. Its maximum length is 50 characters. After you create an `AccessLevel`, you cannot change its `name`.", "type": "string"}, "title": {"description": "Human readable title. Must be unique within the Policy.", "type": "string"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1AccessPolicy": {"description": "`AccessPolicy` is a container for `AccessLevels` (which define the necessary attributes to use Google Cloud services) and `ServicePerimeters` (which define regions of services able to freely pass data within a perimeter). An access policy is globally visible within an organization, and the restrictions it specifies apply to all projects within an organization.", "id": "GoogleIdentityAccesscontextmanagerV1AccessPolicy", "properties": {"etag": {"description": "Output only. An opaque identifier for the current version of the `AccessPolicy`. This will always be a strongly validated etag, meaning that two Access Policies will be identical if and only if their etags are identical. Clients should not expect this to be in any specific format.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Identifier. Resource name of the `AccessPolicy`. Format: `accessPolicies/{access_policy}`", "type": "string"}, "parent": {"description": "Required. The parent of this `AccessPolicy` in the Cloud Resource Hierarchy. Currently immutable once created. Format: `organizations/{organization_id}`", "type": "string"}, "scopes": {"description": "The scopes of the AccessPolicy. Scopes define which resources a policy can restrict and where its resources can be referenced. For example, policy A with `scopes=[\"folders/123\"]` has the following behavior: - ServicePerimeter can only restrict projects within `folders/123`. - ServicePerimeter within policy A can only reference access levels defined within policy A. - Only one policy can include a given scope; thus, attempting to create a second policy which includes `folders/123` will result in an error. If no scopes are provided, then any resource within the organization can be restricted. Scopes cannot be modified after a policy is created. Policies can only have a single scope. Format: list of `folders/{folder_number}` or `projects/{project_number}`", "items": {"type": "string"}, "type": "array"}, "title": {"description": "Required. Human readable title. Does not affect behavior.", "type": "string"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1ApiOperation": {"description": "Identification for an API Operation.", "id": "GoogleIdentityAccesscontextmanagerV1ApiOperation", "properties": {"methodSelectors": {"description": "API methods or permissions to allow. Method or permission must belong to the service specified by `service_name` field. A single MethodSelector entry with `*` specified for the `method` field will allow all methods AND permissions for the service specified in `service_name`.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1MethodSelector"}, "type": "array"}, "serviceName": {"description": "The name of the API whose methods or permissions the IngressPolicy or EgressPolicy want to allow. A single ApiOperation with `service_name` field set to `*` will allow all methods AND permissions for all services.", "type": "string"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1BasicLevel": {"description": "`BasicLevel` is an `AccessLevel` using a set of recommended features.", "id": "GoogleIdentityAccesscontextmanagerV1BasicLevel", "properties": {"combiningFunction": {"description": "How the `conditions` list should be combined to determine if a request is granted this `AccessLevel`. If AND is used, each `Condition` in `conditions` must be satisfied for the `AccessLevel` to be applied. If OR is used, at least one `Condition` in `conditions` must be satisfied for the `AccessLevel` to be applied. Default behavior is AND.", "enum": ["AND", "OR"], "enumDescriptions": ["All `Conditions` must be true for the `BasicLevel` to be true.", "If at least one `Condition` is true, then the `BasicLevel` is true."], "type": "string"}, "conditions": {"description": "Required. A list of requirements for the `AccessLevel` to be granted.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1Condition"}, "type": "array"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1Condition": {"description": "A condition necessary for an `AccessLevel` to be granted. The Condition is an AND over its fields. So a Condition is true if: 1) the request IP is from one of the listed subnetworks AND 2) the originating device complies with the listed device policy AND 3) all listed access levels are granted AND 4) the request was sent at a time allowed by the DateTimeRestriction.", "id": "GoogleIdentityAccesscontextmanagerV1Condition", "properties": {"devicePolicy": {"$ref": "GoogleIdentityAccesscontextmanagerV1DevicePolicy", "description": "Device specific restrictions, all restrictions must hold for the Condition to be true. If not specified, all devices are allowed."}, "ipSubnetworks": {"description": "CIDR block IP subnetwork specification. May be IPv4 or IPv6. Note that for a CIDR IP address block, the specified IP address portion must be properly truncated (i.e. all the host bits must be zero) or the input is considered malformed. For example, \"*********/24\" is accepted but \"*********/24\" is not. Similarly, for IPv6, \"2001:db8::/32\" is accepted whereas \"2001:db8::1/32\" is not. The originating IP of a request must be in one of the listed subnets in order for this Condition to be true. If empty, all IP addresses are allowed.", "items": {"type": "string"}, "type": "array"}, "members": {"description": "The request must be made by one of the provided user or service accounts. Groups are not supported. Syntax: `user:{emailid}` `serviceAccount:{emailid}` If not specified, a request may come from any user.", "items": {"type": "string"}, "type": "array"}, "negate": {"description": "Whether to negate the Condition. If true, the Condition becomes a NAND over its non-empty fields. Any non-empty field criteria evaluating to false will result in the Condition to be satisfied. Defaults to false.", "type": "boolean"}, "regions": {"description": "The request must originate from one of the provided countries/regions. Must be valid ISO 3166-1 alpha-2 codes.", "items": {"type": "string"}, "type": "array"}, "requiredAccessLevels": {"description": "A list of other access levels defined in the same `Policy`, referenced by resource name. Referencing an `AccessLevel` which does not exist is an error. All access levels listed must be granted for the Condition to be true. Example: \"`accessPolicies/MY_POLICY/accessLevels/LEVEL_NAME\"`", "items": {"type": "string"}, "type": "array"}, "vpcNetworkSources": {"description": "The request must originate from one of the provided VPC networks in Google Cloud. Cannot specify this field together with `ip_subnetworks`.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1VpcNetworkSource"}, "type": "array"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1CustomLevel": {"description": "`CustomLevel` is an `AccessLevel` using the Cloud Common Expression Language to represent the necessary conditions for the level to apply to a request. See CEL spec at: https://github.com/google/cel-spec", "id": "GoogleIdentityAccesscontextmanagerV1CustomLevel", "properties": {"expr": {"$ref": "Expr", "description": "Required. A Cloud CEL expression evaluating to a boolean."}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1DevicePolicy": {"description": "`DevicePolicy` specifies device specific restrictions necessary to acquire a given access level. A `DevicePolicy` specifies requirements for requests from devices to be granted access levels, it does not do any enforcement on the device. `DevicePolicy` acts as an AND over all specified fields, and each repeated field is an OR over its elements. Any unset fields are ignored. For example, if the proto is { os_type : DESKTOP_WINDOWS, os_type : DESKTOP_LINUX, encryption_status: ENCRYPTED}, then the DevicePolicy will be true for requests originating from encrypted Linux desktops and encrypted Windows desktops.", "id": "GoogleIdentityAccesscontextmanagerV1DevicePolicy", "properties": {"allowedDeviceManagementLevels": {"description": "Allowed device management levels, an empty list allows all management levels.", "items": {"enum": ["MANAGEMENT_UNSPECIFIED", "NONE", "BASIC", "COMPLETE"], "enumDescriptions": ["The device's management level is not specified or not known.", "The device is not managed.", "Basic management is enabled, which is generally limited to monitoring and wiping the corporate account.", "Complete device management. This includes more thorough monitoring and the ability to directly manage the device (such as remote wiping). This can be enabled through the Android Enterprise Platform."], "type": "string"}, "type": "array"}, "allowedEncryptionStatuses": {"description": "Allowed encryptions statuses, an empty list allows all statuses.", "items": {"enum": ["ENCRYPTION_UNSPECIFIED", "ENCRYPTION_UNSUPPORTED", "UNENCRYPTED", "ENCRYPTED"], "enumDescriptions": ["The encryption status of the device is not specified or not known.", "The device does not support encryption.", "The device supports encryption, but is currently unencrypted.", "The device is encrypted."], "type": "string"}, "type": "array"}, "osConstraints": {"description": "Allowed OS versions, an empty list allows all types and all versions.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1OsConstraint"}, "type": "array"}, "requireAdminApproval": {"description": "Whether the device needs to be approved by the customer admin.", "type": "boolean"}, "requireCorpOwned": {"description": "Whether the device needs to be corp owned.", "type": "boolean"}, "requireScreenlock": {"description": "Whether or not screenlock is required for the DevicePolicy to be true. Defaults to `false`.", "type": "boolean"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1EgressFrom": {"description": "Defines the conditions under which an EgressPolicy matches a request. Conditions based on information about the source of the request. Note that if the destination of the request is also protected by a ServicePerimeter, then that ServicePerimeter must have an IngressPolicy which allows access in order for this request to succeed.", "id": "GoogleIdentityAccesscontextmanagerV1EgressFrom", "properties": {"identities": {"description": "A list of identities that are allowed access through [EgressPolicy]. Identities can be an individual user, service account, Google group, or third-party identity. For third-party identity, only single identities are supported and other identity types are not supported. The `v1` identities that have the prefix `user`, `group`, `serviceAccount`, and `principal` in https://cloud.google.com/iam/docs/principal-identifiers#v1 are supported.", "items": {"type": "string"}, "type": "array"}, "identityType": {"description": "Specifies the type of identities that are allowed access to outside the perimeter. If left unspecified, then members of `identities` field will be allowed access.", "enum": ["IDENTITY_TYPE_UNSPECIFIED", "ANY_IDENTITY", "ANY_USER_ACCOUNT", "ANY_SERVICE_ACCOUNT"], "enumDescriptions": ["No blanket identity group specified.", "Authorize access from all identities outside the perimeter.", "Authorize access from all human users outside the perimeter.", "Authorize access from all service accounts outside the perimeter."], "type": "string"}, "sourceRestriction": {"description": "Whether to enforce traffic restrictions based on `sources` field. If the `sources` fields is non-empty, then this field must be set to `SOURCE_RESTRICTION_ENABLED`.", "enum": ["SOURCE_RESTRICTION_UNSPECIFIED", "SOURCE_RESTRICTION_ENABLED", "SOURCE_RESTRICTION_DISABLED"], "enumDescriptions": ["Enforcement preference unspecified, will not enforce traffic restrictions based on `sources` in EgressFrom.", "Enforcement preference enabled, traffic restrictions will be enforced based on `sources` in EgressFrom.", "Enforcement preference disabled, will not enforce traffic restrictions based on `sources` in EgressFrom."], "type": "string"}, "sources": {"description": "Sources that this EgressPolicy authorizes access from. If this field is not empty, then `source_restriction` must be set to `SOURCE_RESTRICTION_ENABLED`.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1EgressSource"}, "type": "array"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1EgressPolicy": {"description": "Policy for egress from perimeter. EgressPolicies match requests based on `egress_from` and `egress_to` stanzas. For an EgressPolicy to match, both `egress_from` and `egress_to` stanzas must be matched. If an EgressPolicy matches a request, the request is allowed to span the ServicePerimeter boundary. For example, an EgressPolicy can be used to allow VMs on networks within the ServicePerimeter to access a defined set of projects outside the perimeter in certain contexts (e.g. to read data from a Cloud Storage bucket or query against a BigQuery dataset). EgressPolicies are concerned with the *resources* that a request relates as well as the API services and API actions being used. They do not related to the direction of data movement. More detailed documentation for this concept can be found in the descriptions of EgressFrom and EgressTo.", "id": "GoogleIdentityAccesscontextmanagerV1EgressPolicy", "properties": {"egressFrom": {"$ref": "GoogleIdentityAccesscontextmanagerV1EgressFrom", "description": "Defines conditions on the source of a request causing this EgressPolicy to apply."}, "egressTo": {"$ref": "GoogleIdentityAccesscontextmanagerV1EgressTo", "description": "Defines the conditions on the ApiOperation and destination resources that cause this EgressPolicy to apply."}, "title": {"description": "Optional. Human-readable title for the egress rule. The title must be unique within the perimeter and can not exceed 100 characters. Within the access policy, the combined length of all rule titles must not exceed 240,000 characters.", "type": "string"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1EgressSource": {"description": "The source that EgressPolicy authorizes access from inside the ServicePerimeter to somewhere outside the ServicePerimeter boundaries.", "id": "GoogleIdentityAccesscontextmanagerV1EgressSource", "properties": {"accessLevel": {"description": "An AccessLevel resource name that allows protected resources inside the ServicePerimeters to access outside the ServicePerimeter boundaries. AccessLevels listed must be in the same policy as this ServicePerimeter. Referencing a nonexistent AccessLevel will cause an error. If an AccessLevel name is not specified, only resources within the perimeter can be accessed through Google Cloud calls with request origins within the perimeter. Example: `accessPolicies/MY_POLICY/accessLevels/MY_LEVEL`. If a single `*` is specified for `access_level`, then all EgressSources will be allowed.", "type": "string"}, "resource": {"description": "A Google Cloud resource from the service perimeter that you want to allow to access data outside the perimeter. This field supports only projects. The project format is `projects/{project_number}`. You can't use `*` in this field to allow all Google Cloud resources.", "type": "string"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1EgressTo": {"description": "Defines the conditions under which an EgressPolicy matches a request. Conditions are based on information about the ApiOperation intended to be performed on the `resources` specified. Note that if the destination of the request is also protected by a ServicePerimeter, then that ServicePerimeter must have an IngressPolicy which allows access in order for this request to succeed. The request must match `operations` AND `resources` fields in order to be allowed egress out of the perimeter.", "id": "GoogleIdentityAccesscontextmanagerV1EgressTo", "properties": {"externalResources": {"description": "A list of external resources that are allowed to be accessed. Only AWS and Azure resources are supported. For Amazon S3, the supported formats are s3://BUCKET_NAME, s3a://BUCKET_NAME, and s3n://BUCKET_NAME. For Azure Storage, the supported format is azure://myaccount.blob.core.windows.net/CONTAINER_NAME. A request matches if it contains an external resource in this list (Example: s3://bucket/path). Currently '*' is not allowed.", "items": {"type": "string"}, "type": "array"}, "operations": {"description": "A list of ApiOperations allowed to be performed by the sources specified in the corresponding EgressFrom. A request matches if it uses an operation/service in this list.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1ApiOperation"}, "type": "array"}, "resources": {"description": "A list of resources, currently only projects in the form `projects/`, that are allowed to be accessed by sources defined in the corresponding EgressFrom. A request matches if it contains a resource in this list. If `*` is specified for `resources`, then this EgressTo rule will authorize access to all resources outside the perimeter.", "items": {"type": "string"}, "type": "array"}, "roles": {"description": "IAM roles that represent the set of operations that the sources specified in the corresponding EgressFrom. are allowed to perform in this ServicePerimeter.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1IngressFrom": {"description": "Defines the conditions under which an IngressPolicy matches a request. Conditions are based on information about the source of the request. The request must satisfy what is defined in `sources` AND identity related fields in order to match.", "id": "GoogleIdentityAccesscontextmanagerV1IngressFrom", "properties": {"identities": {"description": "A list of identities that are allowed access through [IngressPolicy]. Identities can be an individual user, service account, Google group, or third-party identity. For third-party identity, only single identities are supported and other identity types are not supported. The `v1` identities that have the prefix `user`, `group`, `serviceAccount`, and `principal` in https://cloud.google.com/iam/docs/principal-identifiers#v1 are supported.", "items": {"type": "string"}, "type": "array"}, "identityType": {"description": "Specifies the type of identities that are allowed access from outside the perimeter. If left unspecified, then members of `identities` field will be allowed access.", "enum": ["IDENTITY_TYPE_UNSPECIFIED", "ANY_IDENTITY", "ANY_USER_ACCOUNT", "ANY_SERVICE_ACCOUNT"], "enumDescriptions": ["No blanket identity group specified.", "Authorize access from all identities outside the perimeter.", "Authorize access from all human users outside the perimeter.", "Authorize access from all service accounts outside the perimeter."], "type": "string"}, "sources": {"description": "Sources that this IngressPolicy authorizes access from.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1IngressSource"}, "type": "array"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1IngressPolicy": {"description": "Policy for ingress into ServicePerimeter. IngressPolicies match requests based on `ingress_from` and `ingress_to` stanzas. For an ingress policy to match, both the `ingress_from` and `ingress_to` stanzas must be matched. If an IngressPolicy matches a request, the request is allowed through the perimeter boundary from outside the perimeter. For example, access from the internet can be allowed either based on an AccessLevel or, for traffic hosted on Google Cloud, the project of the source network. For access from private networks, using the project of the hosting network is required. Individual ingress policies can be limited by restricting which services and/or actions they match using the `ingress_to` field.", "id": "GoogleIdentityAccesscontextmanagerV1IngressPolicy", "properties": {"ingressFrom": {"$ref": "GoogleIdentityAccesscontextmanagerV1IngressFrom", "description": "Defines the conditions on the source of a request causing this IngressPolicy to apply."}, "ingressTo": {"$ref": "GoogleIdentityAccesscontextmanagerV1IngressTo", "description": "Defines the conditions on the ApiOperation and request destination that cause this IngressPolicy to apply."}, "title": {"description": "Optional. Human-readable title for the ingress rule. The title must be unique within the perimeter and can not exceed 100 characters. Within the access policy, the combined length of all rule titles must not exceed 240,000 characters.", "type": "string"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1IngressSource": {"description": "The source that IngressPolicy authorizes access from.", "id": "GoogleIdentityAccesscontextmanagerV1IngressSource", "properties": {"accessLevel": {"description": "An AccessLevel resource name that allow resources within the ServicePerimeters to be accessed from the internet. AccessLevels listed must be in the same policy as this ServicePerimeter. Referencing a nonexistent AccessLevel will cause an error. If no AccessLevel names are listed, resources within the perimeter can only be accessed via Google Cloud calls with request origins within the perimeter. Example: `accessPolicies/MY_POLICY/accessLevels/MY_LEVEL`. If a single `*` is specified for `access_level`, then all IngressSources will be allowed.", "type": "string"}, "resource": {"description": "A Google Cloud resource that is allowed to ingress the perimeter. Requests from these resources will be allowed to access perimeter data. Currently only projects and VPCs are allowed. Project format: `projects/{project_number}` VPC network format: `//compute.googleapis.com/projects/{PROJECT_ID}/global/networks/{NAME}`. The project may be in any Google Cloud organization, not just the organization that the perimeter is defined in. `*` is not allowed, the case of allowing all Google Cloud resources only is not supported.", "type": "string"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1IngressTo": {"description": "Defines the conditions under which an IngressPolicy matches a request. Conditions are based on information about the ApiOperation intended to be performed on the target resource of the request. The request must satisfy what is defined in `operations` AND `resources` in order to match.", "id": "GoogleIdentityAccesscontextmanagerV1IngressTo", "properties": {"operations": {"description": "A list of ApiOperations allowed to be performed by the sources specified in corresponding IngressFrom in this ServicePerimeter.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1ApiOperation"}, "type": "array"}, "resources": {"description": "A list of resources, currently only projects in the form `projects/`, protected by this ServicePerimeter that are allowed to be accessed by sources defined in the corresponding IngressFrom. If a single `*` is specified, then access to all resources inside the perimeter are allowed.", "items": {"type": "string"}, "type": "array"}, "roles": {"description": "IAM roles that represent the set of operations that the sources specified in the corresponding IngressFrom are allowed to perform in this ServicePerimeter.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1MethodSelector": {"description": "An allowed method or permission of a service specified in ApiOperation.", "id": "GoogleIdentityAccesscontextmanagerV1MethodSelector", "properties": {"method": {"description": "A valid method name for the corresponding `service_name` in ApiOperation. If `*` is used as the value for the `method`, then ALL methods and permissions are allowed.", "type": "string"}, "permission": {"description": "A valid Cloud IAM permission for the corresponding `service_name` in ApiOperation.", "type": "string"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1OsConstraint": {"description": "A restriction on the OS type and version of devices making requests.", "id": "GoogleIdentityAccesscontextmanagerV1OsConstraint", "properties": {"minimumVersion": {"description": "The minimum allowed OS version. If not set, any version of this OS satisfies the constraint. Format: `\"major.minor.patch\"`. Examples: `\"10.5.301\"`, `\"9.2.1\"`.", "type": "string"}, "osType": {"description": "Required. The allowed OS type.", "enum": ["OS_UNSPECIFIED", "DESKTOP_MAC", "DESKTOP_WINDOWS", "DESKTOP_LINUX", "DESKTOP_CHROME_OS", "ANDROID", "IOS"], "enumDescriptions": ["The operating system of the device is not specified or not known.", "A desktop Mac operating system.", "A desktop Windows operating system.", "A desktop Linux operating system.", "A desktop ChromeOS operating system.", "An Android operating system.", "An iOS operating system."], "type": "string"}, "requireVerifiedChromeOs": {"description": "Only allows requests from devices with a verified Chrome OS. Verifications includes requirements that the device is enterprise-managed, conformant to domain policies, and the caller has permission to call the API targeted by the request.", "type": "boolean"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1ServicePerimeter": {"description": "`ServicePerimeter` describes a set of Google Cloud resources which can freely import and export data amongst themselves, but not export outside of the `ServicePerimeter`. If a request with a source within this `ServicePerimeter` has a target outside of the `ServicePerimeter`, the request will be blocked. Otherwise the request is allowed. There are two types of Service Perimeter - Regular and Bridge. Regular Service Perimeters cannot overlap, a single Google Cloud project or VPC network can only belong to a single regular Service Perimeter. Service Perimeter Bridges can contain only Google Cloud projects as members, a single Google Cloud project may belong to multiple Service Perimeter Bridges.", "id": "GoogleIdentityAccesscontextmanagerV1ServicePerimeter", "properties": {"description": {"description": "Description of the `ServicePerimeter` and its use. Does not affect behavior.", "type": "string"}, "etag": {"description": "Optional. An opaque identifier for the current version of the `ServicePerimeter`. This identifier does not follow any specific format. If an etag is not provided, the operation will be performed as if a valid etag is provided.", "type": "string"}, "name": {"description": "Identifier. Resource name for the `ServicePerimeter`. Format: `accessPolicies/{access_policy}/servicePerimeters/{service_perimeter}`. The `service_perimeter` component must begin with a letter, followed by alphanumeric characters or `_`. After you create a `ServicePerimeter`, you cannot change its `name`.", "type": "string"}, "perimeterType": {"description": "Perimeter type indicator. A single project or VPC network is allowed to be a member of single regular perimeter, but multiple service perimeter bridges. A project cannot be a included in a perimeter bridge without being included in regular perimeter. For perimeter bridges, the restricted service list as well as access level lists must be empty.", "enum": ["PERIMETER_TYPE_REGULAR", "PERIMETER_TYPE_BRIDGE"], "enumDescriptions": ["Regular Perimeter. When no value is specified, the perimeter uses this type.", "Perimeter Bridge."], "type": "string"}, "spec": {"$ref": "GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfig", "description": "Proposed (or dry run) ServicePerimeter configuration. This configuration allows to specify and test ServicePerimeter configuration without enforcing actual access restrictions. Only allowed to be set when the \"use_explicit_dry_run_spec\" flag is set."}, "status": {"$ref": "GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfig", "description": "Current ServicePerimeter configuration. Specifies sets of resources, restricted services and access levels that determine perimeter content and boundaries."}, "title": {"description": "Human readable title. Must be unique within the Policy.", "type": "string"}, "useExplicitDryRunSpec": {"description": "Use explicit dry run spec flag. Ordinarily, a dry-run spec implicitly exists for all Service Perimeters, and that spec is identical to the status for those Service Perimeters. When this flag is set, it inhibits the generation of the implicit spec, thereby allowing the user to explicitly provide a configuration (\"spec\") to use in a dry-run version of the Service Perimeter. This allows the user to test changes to the enforced config (\"status\") without actually enforcing them. This testing is done through analyzing the differences between currently enforced and suggested restrictions. use_explicit_dry_run_spec must bet set to True if any of the fields in the spec are set to non-default values.", "type": "boolean"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfig": {"description": "`ServicePerimeterConfig` specifies a set of Google Cloud resources that describe specific Service Perimeter configuration.", "id": "GoogleIdentityAccesscontextmanagerV1ServicePerimeterConfig", "properties": {"accessLevels": {"description": "A list of `AccessLevel` resource names that allow resources within the `ServicePerimeter` to be accessed from the internet. `AccessLevels` listed must be in the same policy as this `ServicePerimeter`. Referencing a nonexistent `AccessLevel` is a syntax error. If no `AccessLevel` names are listed, resources within the perimeter can only be accessed via Google Cloud calls with request origins within the perimeter. Example: `\"accessPolicies/MY_POLICY/accessLevels/MY_LEVEL\"`. For Service Perimeter Bridge, must be empty.", "items": {"type": "string"}, "type": "array"}, "egressPolicies": {"description": "List of EgressPolicies to apply to the perimeter. A perimeter may have multiple EgressPolicies, each of which is evaluated separately. Access is granted if any EgressPolicy grants it. Must be empty for a perimeter bridge.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1EgressPolicy"}, "type": "array"}, "ingressPolicies": {"description": "List of IngressPolicies to apply to the perimeter. A perimeter may have multiple IngressPolicies, each of which is evaluated separately. Access is granted if any Ingress Policy grants it. Must be empty for a perimeter bridge.", "items": {"$ref": "GoogleIdentityAccesscontextmanagerV1IngressPolicy"}, "type": "array"}, "resources": {"description": "A list of Google Cloud resources that are inside of the service perimeter. Currently only projects and VPCs are allowed. Project format: `projects/{project_number}` VPC network format: `//compute.googleapis.com/projects/{PROJECT_ID}/global/networks/{NAME}`.", "items": {"type": "string"}, "type": "array"}, "restrictedServices": {"description": "Google Cloud services that are subject to the Service Perimeter restrictions. For example, if `storage.googleapis.com` is specified, access to the storage buckets inside the perimeter must meet the perimeter's access restrictions.", "items": {"type": "string"}, "type": "array"}, "vpcAccessibleServices": {"$ref": "GoogleIdentityAccesscontextmanagerV1VpcAccessibleServices", "description": "Configuration for APIs allowed within Perimeter."}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1VpcAccessibleServices": {"description": "Specifies how APIs are allowed to communicate within the Service Perimeter.", "id": "GoogleIdentityAccesscontextmanagerV1VpcAccessibleServices", "properties": {"allowedServices": {"description": "The list of APIs usable within the Service Perimeter. Must be empty unless 'enable_restriction' is True. You can specify a list of individual services, as well as include the 'RESTRICTED-SERVICES' value, which automatically includes all of the services protected by the perimeter.", "items": {"type": "string"}, "type": "array"}, "enableRestriction": {"description": "Whether to restrict API calls within the Service Perimeter to the list of APIs specified in 'allowed_services'.", "type": "boolean"}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1VpcNetworkSource": {"description": "The originating network source in Google Cloud.", "id": "GoogleIdentityAccesscontextmanagerV1VpcNetworkSource", "properties": {"vpcSubnetwork": {"$ref": "GoogleIdentityAccesscontextmanagerV1VpcSubNetwork", "description": "Sub-segment ranges of a VPC network."}}, "type": "object"}, "GoogleIdentityAccesscontextmanagerV1VpcSubNetwork": {"description": "Sub-segment ranges inside of a VPC Network.", "id": "GoogleIdentityAccesscontextmanagerV1VpcSubNetwork", "properties": {"network": {"description": "Required. Network name. If the network is not part of the organization, the `compute.network.get` permission must be granted to the caller. Format: `//compute.googleapis.com/projects/{PROJECT_ID}/global/networks/{NETWORK_NAME}` Example: `//compute.googleapis.com/projects/my-project/global/networks/network-1`", "type": "string"}, "vpcIpSubnetworks": {"description": "CIDR block IP subnetwork specification. The IP address must be an IPv4 address and can be a public or private IP address. Note that for a CIDR IP address block, the specified IP address portion must be properly truncated (i.e. all the host bits must be zero) or the input is considered malformed. For example, \"*********/24\" is accepted but \"*********/24\" is not. If empty, all IP addresses are allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "IamPolicySearchResult": {"description": "The result for an IAM policy search.", "id": "IamPolicySearchResult", "properties": {"explanation": {"$ref": "Explanation", "description": "Explanation about the IAM policy search result. It contains additional information that explains why the search result matches the query."}, "policy": {"$ref": "Policy", "description": "The IAM policy attached to the specified resource. Note that the original IAM policy can contain multiple bindings. This only contains the bindings that match the given query. For queries that don't contain a constraint on policies (e.g. an empty query), this contains all the bindings."}, "project": {"description": "The project that the associated Google Cloud resource belongs to, in the form of `projects/{project_number}`. If an IAM policy is set on a resource -- such as a Compute Engine instance or a Cloud Storage bucket -- the project field will indicate the project that contains the resource. If an IAM policy is set on a folder or organization, the project field will be empty.", "type": "string"}, "resource": {"description": "The [full resource name](https://cloud.google.com/apis/design/resource_names#full_resource_name) of the resource associated with this IAM policy.", "type": "string"}}, "type": "object"}, "Permissions": {"description": "IAM permissions.", "id": "Permissions", "properties": {"permissions": {"description": "A list of permissions. Example permission string: \"compute.disk.get\".", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "SearchAllIamPoliciesResponse": {"description": "Search all IAM policies response.", "id": "SearchAllIamPoliciesResponse", "properties": {"nextPageToken": {"description": "Set if there are more results than those appearing in this response; to get the next set of results, call this method again, using this value as the `page_token`.", "type": "string"}, "results": {"description": "A list of IAM policies that match the search query. Related information such as the associated resource is returned along with the policy.", "items": {"$ref": "IamPolicySearchResult"}, "type": "array"}}, "type": "object"}, "SearchAllResourcesResponse": {"description": "Search all resources response.", "id": "SearchAllResourcesResponse", "properties": {"nextPageToken": {"description": "If there are more results than those appearing in this response, then `next_page_token` is included. To get the next set of results, call this method again using the value of `next_page_token` as `page_token`.", "type": "string"}, "results": {"description": "A list of resource that match the search query.", "items": {"$ref": "StandardResourceMetadata"}, "type": "array"}}, "type": "object"}, "StandardResourceMetadata": {"description": "The standard metadata of a cloud resource.", "id": "StandardResourceMetadata", "properties": {"additionalAttributes": {"description": "Additional searchable attributes of this resource. Informational only. The exact set of attributes is subject to change. For example: project id, DNS name etc.", "items": {"type": "string"}, "type": "array"}, "assetType": {"description": "The type of this resource. For example: \"compute.googleapis.com/Disk\".", "type": "string"}, "description": {"description": "One or more paragraphs of text description of this resource. Maximum length could be up to 1M bytes.", "type": "string"}, "displayName": {"description": "The display name of this resource.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels associated with this resource. See [Labelling and grouping Google Cloud resources](https://cloud.google.com/blog/products/gcp/labelling-and-grouping-your-google-cloud-platform-resources) for more information.", "type": "object"}, "location": {"description": "Location can be \"global\", regional like \"us-east1\", or zonal like \"us-west1-b\".", "type": "string"}, "name": {"description": "The full resource name. For example: `//compute.googleapis.com/projects/my_project_123/zones/zone1/instances/instance1`. See [Resource Names](https://cloud.google.com/apis/design/resource_names#full_resource_name) for more information.", "type": "string"}, "networkTags": {"description": "Network tags associated with this resource. Like labels, network tags are a type of annotations used to group Google Cloud resources. See [Labelling Google Cloud resources](lhttps://cloud.google.com/blog/products/gcp/labelling-and-grouping-your-google-cloud-platform-resources) for more information.", "items": {"type": "string"}, "type": "array"}, "project": {"description": "The project that this resource belongs to, in the form of `projects/{project_number}`.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Asset API", "version": "v1p1beta1", "version_module": true}