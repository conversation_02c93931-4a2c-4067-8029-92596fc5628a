document.addEventListener('DOMContentLoaded', function () {
    // This script now only runs on the results page where data is passed from Jinja2
    if (typeof results1Data !== 'undefined') {
        console.log("Results page loaded. Initializing...");
        
        // Render Product 1's data
        initializeResultColumn(results1Data, '1');

        // Render Product 2's data if it exists
        if (typeof results2Data !== 'undefined' && results2Data) {
            console.log("Comparison data found. Initializing second column.");
            initializeResultColumn(results2Data, '2');
        }
    } else {
        // This script is not needed on the main search page, so it's okay if data is missing there.
        console.log("On a page without analysis data. No charts to render.");
    }

    // --- Image Zoom Functionality (Global for the page) ---
    const imageZoomModal = document.getElementById('imageZoomModal');
    const zoomedImage = document.getElementById('zoomedImage');
    const closeModalSpan = document.querySelector('.image-zoom-close');

    if (imageZoomModal && zoomedImage && closeModalSpan) {
        closeModalSpan.onclick = function() { imageZoomModal.style.display = "none"; }
        window.onclick = function(event) { if (event.target == imageZoomModal) { imageZoomModal.style.display = "none"; } }
    }

    // --- Reusable function to initialize a full column of results ---
    function initializeResultColumn(data, idSuffix) {
        const overallSentimentChartCtx = document.getElementById(`overallSentimentChart${idSuffix}`)?.getContext('2d');
        const aspectChartsContainer = document.getElementById(`aspectChartsContainer${idSuffix}`);
        const noAspectsMessage = document.getElementById(`noAspectsMessage${idSuffix}`);
        const productImageContainer = document.getElementById(`productImageContainer${idSuffix}`);
        const productImage = document.getElementById(`productImage${idSuffix}`);
        const productSpecificationsSnippetElement = document.getElementById(`productSpecificationsSnippet${idSuffix}`);
        
        // Check if essential elements exist for this column before proceeding
        if (!overallSentimentChartCtx || !aspectChartsContainer || !productImageContainer) {
            console.error(`One or more essential elements for column ${idSuffix} are missing from the DOM.`);
            return;
        }

        // --- Image Zoom Click Listener for this specific column ---
        productImageContainer.onclick = function() {
            if (productImage && productImage.src && !productImage.src.endsWith('#')) {
                imageZoomModal.style.display = "block";
                zoomedImage.src = productImage.src;
            }
        }
        
        // --- Render Data for this column ---
        renderColumnData(data, idSuffix, overallSentimentChartCtx, aspectChartsContainer, noAspectsMessage, productSpecificationsSnippetElement);
    }

    function renderColumnData(data, idSuffix, overallCtx, aspectContainer, noAspectsMsg, specElement) {
        console.log(`Rendering data for column ${idSuffix}`);
        
        // Display Specifications Snippet
        specElement.innerHTML = ''; 
        if (data.product_specifications_snippet && !data.product_specifications_snippet.toLowerCase().includes("not found")) {
            let snippetContent = data.product_specifications_snippet;
            const potentialListItems = snippetContent.split(/\s*•\s*|\s*-\s*|\s*\*\s*|\.{3,}|‣/); 
            if (potentialListItems.length > 2 && potentialListItems.some(item => item.trim().length > 5)) {
                const ul = document.createElement('ul'); ul.className = 'list-unstyled ps-0 text-start';
                potentialListItems.forEach(line => {
                    line = line.trim(); if (line) {
                        const li = document.createElement('li'); const colonIndex = line.indexOf(':');
                        if (colonIndex > 0 && colonIndex < 40 && !line.substring(0, colonIndex).includes('\n')) { 
                            li.innerHTML = `<strong>${line.substring(0, colonIndex).trim()}:</strong> ${line.substring(colonIndex + 1).trim()}`;
                        } else { li.textContent = line; }
                        ul.appendChild(li);
                    }
                });
                if (ul.children.length > 0) specElement.appendChild(ul);
                else { const p = document.createElement('p'); p.textContent = snippetContent; p.className = 'text-start'; specElement.appendChild(p); }
            } else { const paragraphs = snippetContent.split('\n'); paragraphs.forEach(paraText => { if (paraText.trim()) { const p = document.createElement('p'); p.textContent = paraText.trim(); p.className = 'text-start'; specElement.appendChild(p); }}); if (specElement.children.length === 0) { const p = document.createElement('p'); p.textContent = snippetContent; p.className = 'text-start'; specElement.appendChild(p); }}
        } else {
            specElement.innerHTML = '<p style="color: #6c757d;" class="text-start">Specifications data not available.</p>';
        }

        // Render Overall Sentiment Chart
        const totalOverallSentiments = (data.overall_sentiment?.positive || 0) + (data.overall_sentiment?.negative || 0) + (data.overall_sentiment?.neutral || 0);
        if (totalOverallSentiments > 0) {
            overallCtx.canvas.parentElement.style.display = 'block';
            new Chart(overallCtx, { type: 'pie', data: { labels: ['Positive', 'Negative', 'Neutral'], datasets: [{ label: 'Overall Sentiment', data: [data.overall_sentiment.positive, data.overall_sentiment.negative, data.overall_sentiment.neutral], backgroundColor: ['rgba(75, 192, 192, 0.7)', 'rgba(255, 99, 132, 0.7)', 'rgba(201, 203, 207, 0.7)'], borderWidth: 1 }] }, options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'top' }, title: { display: true, text: 'Overall Tweet Sentiment' }}}});
        } else { overallCtx.canvas.parentElement.style.display = 'none'; }

        // Render Aspect Charts
        aspectContainer.innerHTML = '';
        const aspectsPresent = data.aspect_sentiments && Object.keys(data.aspect_sentiments).length > 0;
        if (aspectsPresent) {
            noAspectsMsg.style.display = 'none'; aspectContainer.style.display = 'flex';
            for (const aspect in data.aspect_sentiments) {
                const aspectData = data.aspect_sentiments[aspect]; if (aspectData.mentions === 0) continue;
                const colDiv = document.createElement('div'); colDiv.className = 'col-12 col-md-6 mb-4'; 
                const chartContainerDiv = document.createElement('div'); chartContainerDiv.className = 'chart-container'; chartContainerDiv.style.height = '250px';
                const canvas = document.createElement('canvas');
                chartContainerDiv.appendChild(canvas); colDiv.appendChild(chartContainerDiv); aspectContainer.appendChild(colDiv);
                new Chart(canvas.getContext('2d'), { type: 'bar', data: { labels: ['Positive', 'Negative', 'Neutral'], datasets: [{ label: `${aspect} (Mentions: ${aspectData.mentions})`, data: [aspectData.positive, aspectData.negative, aspectData.neutral], backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(255, 99, 132, 0.6)', 'rgba(201, 203, 207, 0.6)'], borderWidth: 1 }] }, options: { responsive: true, maintainAspectRatio: false, indexAxis: 'y', scales: { x: { beginAtZero: true, title: { display: true, text: '# Mentions' }}}, plugins: { legend: { display: false }, title: { display: true, text: `Sentiment: ${aspect.charAt(0).toUpperCase() + aspect.slice(1)}` }}}});
            }
        } else {
            noAspectsMsg.style.display = 'block'; aspectContainer.style.display = 'none';
        }
    }
});