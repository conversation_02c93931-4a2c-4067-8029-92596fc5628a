document.addEventListener('DOMContentLoaded', function () {
    console.log("DOM fully loaded and parsed."); 

    // --- Element Selectors ---
    const sentimentForm = document.getElementById('sentimentForm');
    const resultsSection = document.getElementById('resultsSection');
    const productImageContainer = document.getElementById('productImageContainer'); 
    const productImage = document.getElementById('productImage');
    const noImageText = document.getElementById('noImageText');
    const overallSentimentChartCtx = document.getElementById('overallSentimentChart').getContext('2d');
    const aspectChartsContainer = document.getElementById('aspectChartsContainer');
    const noAspectsMessage = document.getElementById('noAspectsMessage');
    const analyzedProductNameHeader = document.getElementById('analyzedProductNameHeader');
    const tweetsCountElement = document.getElementById('tweetsCount');
    const sampleTweetsList = document.getElementById('sampleTweetsList');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const apiErrorMessageElement = document.getElementById('apiErrorMessage');
    const productSpecificationsSnippetElement = document.getElementById('productSpecificationsSnippet');
    const imageZoomModal = document.getElementById('imageZoomModal');
    const zoomedImage = document.getElementById('zoomedImage');
    const closeModalSpan = document.querySelector('.image-zoom-close');
    const searchHistoryList = document.getElementById('searchHistoryList');
    const productNameInput = document.getElementById('productName');

    let overallChartInstance = null;
    const aspectChartInstances = {};

    // --- Main Submit Handler ---
    if (sentimentForm) {
        sentimentForm.addEventListener('submit', function (e) {
            e.preventDefault(); 
            const productName = productNameInput.value;
            if (productName && productName.trim() !== "") {
                runAnalysis(productName);
            } else {
                apiErrorMessageElement.textContent = 'Product name is required.';
                apiErrorMessageElement.style.display = 'block';
            }
        });
    } else {
        console.error("CRITICAL: Sentiment form with ID 'sentimentForm' not found in the DOM!");
    }

    // --- NEW: Compare Form Handler ---
    const compareForm = document.getElementById('compareForm');
    if (compareForm) {
        compareForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const product1Name = document.getElementById('product1Name').value;
            const product2Name = document.getElementById('product2Name').value;

            if (product1Name && product2Name && product1Name.trim() !== "" && product2Name.trim() !== "") {
                // Redirect to the comparison page with product names as URL parameters
                window.location.href = `/compare_page?product1=${encodeURIComponent(product1Name)}&product2=${encodeURIComponent(product2Name)}`;
            } else {
                alert("Please enter both product names for comparison.");
            }
        });
    }

    // --- Search History Click Handler ---
    if (searchHistoryList) {
        searchHistoryList.addEventListener('click', function(e) {
            e.preventDefault();
            const targetLink = e.target.closest('.history-item'); 
            if (targetLink) {
                const productName = targetLink.dataset.productName;
                console.log(`History item clicked: ${productName}`);
                if (productName) {
                    productNameInput.value = productName; 
                    runAnalysis(productName); 
                }
            }
        });
    }

    // --- Image Zoom Functionality ---
    if (productImageContainer && imageZoomModal && zoomedImage && closeModalSpan) {
        productImageContainer.onclick = function() {
            if (productImage.style.display === 'block' && productImage.src && productImage.src !== '#') {
                imageZoomModal.style.display = "block";
                zoomedImage.src = productImage.src;
            }
        }
        closeModalSpan.onclick = function() { imageZoomModal.style.display = "none"; }
        window.onclick = function(event) { if (event.target == imageZoomModal) { imageZoomModal.style.display = "none"; } }
    }

    // --- Reusable Analysis Function ---
    async function runAnalysis(productName) {
        console.log(`Running analysis for: ${productName}`);
        loadingSpinner.style.display = 'inline-block';
        resultsSection.style.display = 'none';
        apiErrorMessageElement.style.display = 'none';
        apiErrorMessageElement.textContent = '';
        noImageText.textContent = 'Searching for image...';
        productSpecificationsSnippetElement.innerHTML = '<p style="color: #6c757d;" class="text-start">Fetching specifications...</p>';

        const requestBody = { product_name: productName };
        console.log("Attempting to fetch /analyze with body:", JSON.stringify(requestBody)); 

        try {
            const response = await fetch('/analyze', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(requestBody),
            });
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.error || `Server error: ${response.status}`);
            }
            if (data.error_message) {
                apiErrorMessageElement.textContent = `Info: ${data.error_message}`;
                apiErrorMessageElement.style.display = 'block';
            }
            displayResults(data);
        } catch (error) {
            console.error('Catch block: Error during analysis request:', error);
            apiErrorMessageElement.textContent = `Application Error: ${error.message}`;
            apiErrorMessageElement.style.display = 'block';
            resultsSection.style.display = 'none';
        } finally {
            console.log("Fetch request finished."); 
            loadingSpinner.style.display = 'none';
        }
    }


    function displayResults(data) {
        // This function is long, but it remains the same as the last version I provided.
        // It handles populating all the charts and data for a *single* product analysis.
        // I'm including it here for completeness.
        console.log("Displaying results with data:", data);
        resultsSection.style.display = 'block';
        analyzedProductNameHeader.textContent = `Results for: ${data.product_name}`;
        if (data.tweets_count > 0) {
            tweetsCountElement.textContent = `Based on ${data.tweets_count} tweets.`;
        } else if (data.error_message && data.error_message.toLowerCase().includes("twitter")) {
            tweetsCountElement.textContent = "Could not fetch tweets (see info message above).";
        } else {
            tweetsCountElement.textContent = "No tweets found or analyzed for sentiment.";
        }
        if (data.product_image_url) {
            productImage.src = data.product_image_url;
            productImage.style.display = 'block'; noImageText.style.display = 'none';
            productImageContainer.style.cursor = 'pointer'; 
            productImage.onerror = () => { 
                productImage.style.display = 'none'; noImageText.style.display = 'block';
                noImageText.textContent = 'Image could not be loaded or was not found.';
                productImageContainer.style.cursor = 'default';
            };
        } else {
            productImage.style.display = 'none'; noImageText.style.display = 'block';
            noImageText.textContent = 'No product image found.';
            productImageContainer.style.cursor = 'default';
        }
        productSpecificationsSnippetElement.innerHTML = ''; 
        if (data.product_specifications_snippet && !data.product_specifications_snippet.toLowerCase().includes("not found")) {
            let snippetContent = data.product_specifications_snippet;
            const potentialListItems = snippetContent.split(/\s*•\s*|\s*-\s*|\s*\*\s*|\.{3,}|‣/); 
            if (potentialListItems.length > 2 && potentialListItems.some(item => item.trim().length > 5)) {
                const ul = document.createElement('ul'); ul.className = 'list-unstyled ps-0 text-start';
                potentialListItems.forEach(line => {
                    line = line.trim();
                    if (line) {
                        const li = document.createElement('li'); const colonIndex = line.indexOf(':');
                        if (colonIndex > 0 && colonIndex < 40 && !line.substring(0, colonIndex).includes('\n')) { 
                            li.innerHTML = `<strong>${line.substring(0, colonIndex).trim()}:</strong> ${line.substring(colonIndex + 1).trim()}`;
                        } else { li.textContent = line; }
                        ul.appendChild(li);
                    }
                });
                if (ul.children.length > 0) { productSpecificationsSnippetElement.appendChild(ul); }
                else { const p = document.createElement('p'); p.textContent = snippetContent; p.className = 'text-start'; productSpecificationsSnippetElement.appendChild(p); }
            } else {
                const paragraphs = snippetContent.split('\n');
                paragraphs.forEach(paraText => {
                    if (paraText.trim()) { const p = document.createElement('p'); p.textContent = paraText.trim(); p.className = 'text-start'; productSpecificationsSnippetElement.appendChild(p); }
                });
                if (productSpecificationsSnippetElement.children.length === 0) { const p = document.createElement('p'); p.textContent = snippetContent; p.className = 'text-start'; productSpecificationsSnippetElement.appendChild(p); }
            }
        } else { productSpecificationsSnippetElement.innerHTML = '<p style="color: #6c757d;" class="text-start">Specifications data not available.</p>'; }
        if (overallChartInstance) { overallChartInstance.destroy(); }
        const totalOverallSentiments = (data.overall_sentiment?.positive || 0) + (data.overall_sentiment?.negative || 0) + (data.overall_sentiment?.neutral || 0);
        const overallChartContainer = document.getElementById('overallSentimentChart').parentElement;
        if (totalOverallSentiments > 0) {
           overallChartContainer.style.display = 'block';
            overallChartInstance = new Chart(overallSentimentChartCtx, { type: 'pie', data: { labels: ['Positive', 'Negative', 'Neutral'], datasets: [{ label: 'Overall Sentiment', data: [ data.overall_sentiment.positive, data.overall_sentiment.negative, data.overall_sentiment.neutral ], backgroundColor: ['rgba(75, 192, 192, 0.7)', 'rgba(255, 99, 132, 0.7)', 'rgba(201, 203, 207, 0.7)'], borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(201, 203, 207, 1)'], borderWidth: 1 }] }, options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'top' }, title: { display: true, text: 'Overall Tweet Sentiment' }}}});
        } else { overallChartContainer.style.display = 'none'; }
        aspectChartsContainer.innerHTML = ''; Object.keys(aspectChartInstances).forEach(key => { if(aspectChartInstances[key]) aspectChartInstances[key].destroy(); });
        const aspectsPresent = data.aspect_sentiments && Object.keys(data.aspect_sentiments).length > 0;
        if (aspectsPresent) {
            noAspectsMessage.style.display = 'none'; aspectChartsContainer.style.display = 'flex'; 
            for (const aspect in data.aspect_sentiments) {
                const aspectData = data.aspect_sentiments[aspect]; if (aspectData.mentions === 0) continue;
                const colDiv = document.createElement('div'); colDiv.className = 'col-md-6 col-lg-4 mb-4'; const chartContainerDiv = document.createElement('div'); chartContainerDiv.className = 'chart-container'; chartContainerDiv.style.height = '300px'; const canvas = document.createElement('canvas'); canvas.id = `aspectChart-${aspect}`; chartContainerDiv.appendChild(canvas); colDiv.appendChild(chartContainerDiv); aspectChartsContainer.appendChild(colDiv);
                aspectChartInstances[aspect] = new Chart(canvas.getContext('2d'), { type: 'bar', data: { labels: ['Positive', 'Negative', 'Neutral'], datasets: [{ label: `${aspect} (Mentions: ${aspectData.mentions})`, data: [aspectData.positive, aspectData.negative, aspectData.neutral], backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(255, 99, 132, 0.6)', 'rgba(201, 203, 207, 0.6)'], borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(201, 203, 207, 1)'], borderWidth: 1 }] }, options: { responsive: true, maintainAspectRatio: false, indexAxis: 'y', scales: { x: { beginAtZero: true, title: { display: true, text: '# Mentions' }}}, plugins: { legend: { display: false }, title: { display: true, text: `Sentiment: ${aspect.charAt(0).toUpperCase() + aspect.slice(1)}` }}}});
            }
        } else { noAspectsMessage.style.display = 'block'; aspectChartsContainer.style.display = 'none'; }
        sampleTweetsList.innerHTML = '';
        if (data.sample_tweets && data.sample_tweets.length > 0 && !(data.sample_tweets[0].text.toLowerCase().includes("could not fetch") || data.sample_tweets[0].text.toLowerCase().includes("no relevant tweets"))) {
            data.sample_tweets.forEach(tweet => {
                const li = document.createElement('li'); li.className = 'list-group-item'; let sentimentClass = '';
                if (tweet.sentiment === 'positive') sentimentClass = 'text-success';
                else if (tweet.sentiment === 'negative') sentimentClass = 'text-danger';
                else sentimentClass = 'text-secondary';
                li.innerHTML = `<span class="fw-bold ${sentimentClass}">[${tweet.sentiment.toUpperCase()}]</span>: ${tweet.text}`;
                sampleTweetsList.appendChild(li);
            });
        } else {
            const li = document.createElement('li'); li.className = 'list-group-item';
            li.textContent = data.sample_tweets && data.sample_tweets.length > 0 ? data.sample_tweets[0].text : 'No sample tweets to display.';
            li.style.color = '#6c757d'; sampleTweetsList.appendChild(li);
        }
    }
});