document.addEventListener('DOMContentLoaded', function () {
    console.log("DOM fully loaded and parsed."); // 1. Check if this runs

    const sentimentForm = document.getElementById('sentimentForm');
    const resultsSection = document.getElementById('resultsSection');
    const productImage = document.getElementById('productImage');
    const noImageText = document.getElementById('noImageText');
    const overallSentimentChartCtx = document.getElementById('overallSentimentChart').getContext('2d');
    const aspectChartsContainer = document.getElementById('aspectChartsContainer');
    const noAspectsMessage = document.getElementById('noAspectsMessage');
    const analyzedProductNameHeader = document.getElementById('analyzedProductNameHeader');
    const tweetsCountElement = document.getElementById('tweetsCount');
    const sampleTweetsList = document.getElementById('sampleTweetsList');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const apiErrorMessageElement = document.getElementById('apiErrorMessage');
    const productSpecificationsSnippetElement = document.getElementById('productSpecificationsSnippet');

    let overallChartInstance = null;
    const aspectChartInstances = {};

    console.log("Sentiment form element:", sentimentForm); // 2. Check if form element is found

    if (sentimentForm) {
        sentimentForm.addEventListener('submit', async function (e) {
            console.log("Form submit event triggered!"); // 3. Check if this event listener fires

            e.preventDefault(); 
            console.log("Default form submission prevented."); // 4. Confirm prevention

            loadingSpinner.style.display = 'inline-block';
            resultsSection.style.display = 'none';
            apiErrorMessageElement.style.display = 'none';
            apiErrorMessageElement.textContent = '';
            noImageText.textContent = 'Searching for image...';
            productSpecificationsSnippetElement.innerHTML = '<p style="color: #6c757d;">Fetching specifications...</p>';

            const productNameInput = document.getElementById('productName');
            if (!productNameInput) {
                console.error("Product name input field not found!");
                loadingSpinner.style.display = 'none';
                return;
            }
            const productName = productNameInput.value;
            console.log("Product name from input:", productName); // 5. Check product name value

            if (!productName || productName.trim() === "") {
                console.warn("Product name is empty.");
                apiErrorMessageElement.textContent = 'Product name is required.';
                apiErrorMessageElement.style.display = 'block';
                loadingSpinner.style.display = 'none';
                return;
            }

            const requestBody = { product_name: productName };
            console.log("Attempting to fetch /analyze with body:", JSON.stringify(requestBody)); // 6. Log before fetch

            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody),
                });
                console.log("Fetch response status:", response.status); // 7. Log response status

                const data = await response.json();
                console.log("Data received from /analyze:", data); // 8. Log received data

                if (!response.ok) {
                    console.error("Server responded with an error:", data.error || response.statusText);
                    throw new Error(data.error || `Server error: ${response.status}`);
                }

                if (data.error_message) {
                    apiErrorMessageElement.textContent = `Info: ${data.error_message}`;
                    apiErrorMessageElement.style.display = 'block';
                } else {
                    apiErrorMessageElement.style.display = 'none';
                }

                displayResults(data);

            } catch (error) {
                console.error('Catch block: Error during sentiment analysis request or processing:', error);
                apiErrorMessageElement.textContent = `Application Error: ${error.message}`;
                apiErrorMessageElement.style.display = 'block';
                resultsSection.style.display = 'none';
            } finally {
                console.log("Fetch request finished (finally block)."); // 9. Log end of fetch attempt
                loadingSpinner.style.display = 'none';
            }
        });
        console.log("Submit event listener attached to form."); // Check listener attachment
    } else {
        console.error("CRITICAL: Sentiment form with ID 'sentimentForm' not found in the DOM!");
    }

    function displayResults(data) {
        console.log("Displaying results with data:", data);
        resultsSection.style.display = 'block';
        analyzedProductNameHeader.textContent = `Results for: ${data.product_name}`;
        
        if (data.tweets_count > 0) {
            tweetsCountElement.textContent = `Based on ${data.tweets_count} tweets.`;
        } else if (data.error_message && data.error_message.toLowerCase().includes("twitter")) {
            tweetsCountElement.textContent = "Could not fetch tweets (see info message above).";
        } else {
            tweetsCountElement.textContent = "No tweets found or analyzed for sentiment.";
        }

        if (data.product_image_url) {
            productImage.src = data.product_image_url;
            productImage.style.display = 'block';
            noImageText.style.display = 'none';
            productImage.onerror = () => { 
                productImage.style.display = 'none';
                noImageText.style.display = 'block';
                noImageText.textContent = 'Image could not be loaded or was not found.';
            };
        } else {
            productImage.style.display = 'none';
            noImageText.style.display = 'block';
            noImageText.textContent = 'No product image found.';
        }

        if (data.product_specifications_snippet && data.product_specifications_snippet !== "Could not retrieve specifications snippet." && data.product_specifications_snippet !== "Could not retrieve detailed specifications snippet.") {
            productSpecificationsSnippetElement.innerHTML = ''; 
            const lines = data.product_specifications_snippet.split(/\n|\s*•\s*|\s*-\s*|\s*\*\s*/);
            lines.forEach(line => {
                line = line.trim();
                if (line) {
                    const p = document.createElement('p');
                    p.textContent = line;
                    productSpecificationsSnippetElement.appendChild(p);
                }
            });
            if (productSpecificationsSnippetElement.children.length === 0) {
                 productSpecificationsSnippetElement.innerHTML = `<p>${data.product_specifications_snippet}</p>`;
            }
        } else {
            productSpecificationsSnippetElement.innerHTML = '<p style="color: #6c757d;">Specifications data not available or could not be retrieved.</p>';
        }


        if (overallChartInstance) {
            overallChartInstance.destroy();
        }
        const totalOverallSentiments = (data.overall_sentiment?.positive || 0) + (data.overall_sentiment?.negative || 0) + (data.overall_sentiment?.neutral || 0);
        const overallChartContainer = document.getElementById('overallSentimentChart').parentElement;
        
        if (totalOverallSentiments > 0) {
           overallChartContainer.style.display = 'block';
            overallChartInstance = new Chart(overallSentimentChartCtx, {
                type: 'pie',
                data: {
                    labels: ['Positive', 'Negative', 'Neutral'],
                    datasets: [{
                        label: 'Overall Sentiment',
                        data: [
                            data.overall_sentiment.positive,
                            data.overall_sentiment.negative,
                            data.overall_sentiment.neutral
                        ],
                        backgroundColor: ['rgba(75, 192, 192, 0.7)', 'rgba(255, 99, 132, 0.7)', 'rgba(201, 203, 207, 0.7)'],
                        borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(201, 203, 207, 1)'],
                        borderWidth: 1
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'top' }, title: { display: true, text: 'Overall Tweet Sentiment' }}}
            });
        } else {
             overallChartContainer.style.display = 'none';
        }

        aspectChartsContainer.innerHTML = ''; 
        Object.keys(aspectChartInstances).forEach(key => {
            if(aspectChartInstances[key]) aspectChartInstances[key].destroy();
        });

        const aspectsPresent = data.aspect_sentiments && Object.keys(data.aspect_sentiments).length > 0;
        if (aspectsPresent) {
            noAspectsMessage.style.display = 'none';
            aspectChartsContainer.style.display = 'flex'; 
            for (const aspect in data.aspect_sentiments) {
                const aspectData = data.aspect_sentiments[aspect];
                if (aspectData.mentions === 0) continue;

                const colDiv = document.createElement('div');
                colDiv.className = 'col-md-6 col-lg-4 mb-4'; 
                const chartContainerDiv = document.createElement('div');
                chartContainerDiv.className = 'chart-container';
                chartContainerDiv.style.height = '300px';
                const canvas = document.createElement('canvas');
                canvas.id = `aspectChart-${aspect}`;
                chartContainerDiv.appendChild(canvas);
                colDiv.appendChild(chartContainerDiv);
                aspectChartsContainer.appendChild(colDiv);

                aspectChartInstances[aspect] = new Chart(canvas.getContext('2d'), {
                    type: 'bar', 
                    data: {
                        labels: ['Positive', 'Negative', 'Neutral'],
                        datasets: [{
                            label: `${aspect} (Mentions: ${aspectData.mentions})`,
                            data: [aspectData.positive, aspectData.negative, aspectData.neutral],
                            backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(255, 99, 132, 0.6)', 'rgba(201, 203, 207, 0.6)'],
                            borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(201, 203, 207, 1)'],
                            borderWidth: 1
                        }]
                    },
                    options: { responsive: true, maintainAspectRatio: false, indexAxis: 'y', scales: { x: { beginAtZero: true, title: { display: true, text: '# Mentions' }}}, plugins: { legend: { display: false }, title: { display: true, text: `Sentiment: ${aspect.charAt(0).toUpperCase() + aspect.slice(1)}` }}}
                });
            }
        } else {
            noAspectsMessage.style.display = 'block';
            aspectChartsContainer.style.display = 'none';
        }
        
        sampleTweetsList.innerHTML = '';
        if (data.sample_tweets && data.sample_tweets.length > 0 && !(data.sample_tweets[0].text.toLowerCase().includes("could not fetch") || data.sample_tweets[0].text.toLowerCase().includes("no relevant tweets"))) {
            data.sample_tweets.forEach(tweet => {
                const li = document.createElement('li');
                li.className = 'list-group-item';
                let sentimentClass = '';
                if (tweet.sentiment === 'positive') sentimentClass = 'text-success';
                else if (tweet.sentiment === 'negative') sentimentClass = 'text-danger';
                else sentimentClass = 'text-secondary';
                
                li.innerHTML = `<span class="fw-bold ${sentimentClass}">[${tweet.sentiment.toUpperCase()}]</span>: ${tweet.text}`;
                sampleTweetsList.appendChild(li);
            });
        } else {
            const li = document.createElement('li');
            li.className = 'list-group-item';
            li.textContent = data.sample_tweets && data.sample_tweets.length > 0 ? data.sample_tweets[0].text : 'No sample tweets to display.';
            li.style.color = '#6c757d';
            sampleTweetsList.appendChild(li);
        }
    }
});