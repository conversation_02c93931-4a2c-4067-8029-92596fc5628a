{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://solar.googleapis.com/", "batchPath": "batch", "canonicalName": "Solar", "description": "Solar API.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/maps/documentation/solar", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "solar:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://solar.mtls.googleapis.com/", "name": "solar", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"buildingInsights": {"methods": {"findClosest": {"description": "Locates the building whose centroid is closest to a query point. Returns an error with code `NOT_FOUND` if there are no buildings within approximately 50m of the query point.", "flatPath": "v1/buildingInsights:findClosest", "httpMethod": "GET", "id": "solar.buildingInsights.findClosest", "parameterOrder": [], "parameters": {"experiments": {"description": "Optional. Specifies the pre-GA features to enable.", "enum": ["EXPERIMENT_UNSPECIFIED", "EXPANDED_COVERAGE"], "enumDescriptions": ["No experiments are specified.", "Expands the geographic region available for querying solar data. For more information, see [Expanded Coverage](https://developers.google.com/maps/documentation/solar/expanded-coverage)."], "location": "query", "repeated": true, "type": "string"}, "location.latitude": {"description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double", "location": "query", "type": "number"}, "location.longitude": {"description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "format": "double", "location": "query", "type": "number"}, "requiredQuality": {"description": "Optional. The minimum quality level allowed in the results. No result with lower quality than this will be returned. Not specifying this is equivalent to restricting to HIGH quality only.", "enum": ["IMAGERY_QUALITY_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "BASE"], "enumDescriptions": ["No quality is known.", "Solar data is derived from aerial imagery captured at low-altitude and processed at 0.1 m/pixel.", "Solar data is derived from enhanced aerial imagery captured at high-altitude and processed at 0.25 m/pixel.", "Solar data is derived from enhanced satellite imagery processed at 0.25 m/pixel.", "Solar data is derived from enhanced satellite imagery processed at 0.25 m/pixel. **Note:** This enum is only available if `experiments=EXPANDED_COVERAGE` is set in the request. For more information, see [Expanded Coverage](https://developers.google.com/maps/documentation/solar/expanded-coverage)."], "location": "query", "type": "string"}}, "path": "v1/buildingInsights:findClosest", "response": {"$ref": "BuildingInsights"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "dataLayers": {"methods": {"get": {"description": "Gets solar information for a region surrounding a location. Returns an error with code `NOT_FOUND` if the location is outside the coverage area.", "flatPath": "v1/dataLayers:get", "httpMethod": "GET", "id": "solar.dataLayers.get", "parameterOrder": [], "parameters": {"exactQualityRequired": {"description": "Optional. Whether to require exact quality of the imagery. If set to false, the `required_quality` field is interpreted as the minimum required quality, such that HIGH quality imagery may be returned when `required_quality` is set to MEDIUM. If set to true, `required_quality` is interpreted as the exact required quality and only `MEDIUM` quality imagery is returned if `required_quality` is set to `MEDIUM`.", "location": "query", "type": "boolean"}, "experiments": {"description": "Optional. Specifies the pre-GA experiments to enable.", "enum": ["EXPERIMENT_UNSPECIFIED", "EXPANDED_COVERAGE"], "enumDescriptions": ["No experiments are specified.", "Expands the geographic region available for querying solar data. For more information, see [Expanded Coverage](https://developers.google.com/maps/documentation/solar/expanded-coverage)."], "location": "query", "repeated": true, "type": "string"}, "location.latitude": {"description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double", "location": "query", "type": "number"}, "location.longitude": {"description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "format": "double", "location": "query", "type": "number"}, "pixelSizeMeters": {"description": "Optional. The minimum scale, in meters per pixel, of the data to return. Values of 0.1 (the default, if this field is not set explicitly), 0.25, 0.5, and 1.0 are supported. Imagery components whose normal resolution is less than `pixel_size_meters` will be returned at the resolution specified by `pixel_size_meters`; imagery components whose normal resolution is equal to or greater than `pixel_size_meters` will be returned at that normal resolution.", "format": "float", "location": "query", "type": "number"}, "radiusMeters": {"description": "Required. The radius, in meters, defining the region surrounding that centre point for which data should be returned. The limitations on this value are: * Any value up to 100m can always be specified. * Values over 100m can be specified, as long as `radius_meters` <= `pixel_size_meters * 1000`. * However, for values over 175m, the `DataLayerView` in the request must not include monthly flux or hourly shade.", "format": "float", "location": "query", "type": "number"}, "requiredQuality": {"description": "Optional. The minimum quality level allowed in the results. No result with lower quality than this will be returned. Not specifying this is equivalent to restricting to HIGH quality only.", "enum": ["IMAGERY_QUALITY_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "BASE"], "enumDescriptions": ["No quality is known.", "Solar data is derived from aerial imagery captured at low-altitude and processed at 0.1 m/pixel.", "Solar data is derived from enhanced aerial imagery captured at high-altitude and processed at 0.25 m/pixel.", "Solar data is derived from enhanced satellite imagery processed at 0.25 m/pixel.", "Solar data is derived from enhanced satellite imagery processed at 0.25 m/pixel. **Note:** This enum is only available if `experiments=EXPANDED_COVERAGE` is set in the request. For more information, see [Expanded Coverage](https://developers.google.com/maps/documentation/solar/expanded-coverage)."], "location": "query", "type": "string"}, "view": {"description": "Optional. The desired subset of the data to return.", "enum": ["DATA_LAYER_VIEW_UNSPECIFIED", "DSM_LAYER", "IMAGERY_LAYERS", "IMAGERY_AND_ANNUAL_FLUX_LAYERS", "IMAGERY_AND_ALL_FLUX_LAYERS", "FULL_LAYERS"], "enumDescriptions": ["Equivalent to FULL.", "Get the DSM only.", "Get the DSM, RGB, and mask.", "Get the DSM, RGB, mask, and annual flux.", "Get the DSM, RGB, mask, annual flux, and monthly flux.", "Get all data."], "location": "query", "type": "string"}}, "path": "v1/dataLayers:get", "response": {"$ref": "DataLayers"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "geoTiff": {"methods": {"get": {"description": "Returns an image by its ID.", "flatPath": "v1/geoTiff:get", "httpMethod": "GET", "id": "solar.geoTiff.get", "parameterOrder": [], "parameters": {"id": {"description": "Required. The ID of the asset being requested.", "location": "query", "type": "string"}}, "path": "v1/geoTiff:get", "response": {"$ref": "HttpBody"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}, "revision": "20250427", "rootUrl": "https://solar.googleapis.com/", "schemas": {"BuildingInsights": {"description": "Response message for `Solar.FindClosestBuildingInsights`. Information about the location, dimensions, and solar potential of a building.", "id": "BuildingInsights", "properties": {"administrativeArea": {"description": "Administrative area 1 (e.g., in the US, the state) that contains this building. For example, in the US, the abbreviation might be \"MA\" or \"CA.\"", "type": "string"}, "boundingBox": {"$ref": "LatLngBox", "description": "The bounding box of the building."}, "center": {"$ref": "LatLng", "description": "A point near the center of the building."}, "imageryDate": {"$ref": "Date", "description": "Date that the underlying imagery was acquired. This is approximate."}, "imageryProcessedDate": {"$ref": "Date", "description": "When processing was completed on this imagery."}, "imageryQuality": {"description": "The quality of the imagery used to compute the data for this building.", "enum": ["IMAGERY_QUALITY_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "BASE"], "enumDescriptions": ["No quality is known.", "Solar data is derived from aerial imagery captured at low-altitude and processed at 0.1 m/pixel.", "Solar data is derived from enhanced aerial imagery captured at high-altitude and processed at 0.25 m/pixel.", "Solar data is derived from enhanced satellite imagery processed at 0.25 m/pixel.", "Solar data is derived from enhanced satellite imagery processed at 0.25 m/pixel. **Note:** This enum is only available if `experiments=EXPANDED_COVERAGE` is set in the request. For more information, see [Expanded Coverage](https://developers.google.com/maps/documentation/solar/expanded-coverage)."], "type": "string"}, "name": {"description": "The resource name for the building, of the format `buildings/{place_id}`.", "type": "string"}, "postalCode": {"description": "Postal code (e.g., US zip code) this building is contained by.", "type": "string"}, "regionCode": {"description": "Region code for the country (or region) this building is in.", "type": "string"}, "solarPotential": {"$ref": "SolarPotential", "description": "Solar potential of the building."}, "statisticalArea": {"description": "Statistical area (e.g., US census tract) this building is in.", "type": "string"}}, "type": "object"}, "CashPurchaseSavings": {"description": "Cost and benefit of an outright purchase of a particular configuration of solar panels with a particular electricity usage.", "id": "CashPurchaseSavings", "properties": {"outOfPocketCost": {"$ref": "Money", "description": "Initial cost before tax incentives: the amount that must be paid out-of-pocket. Contrast with `upfront_cost`, which is after tax incentives."}, "paybackYears": {"description": "Number of years until payback occurs. A negative value means payback never occurs within the lifetime period.", "format": "float", "type": "number"}, "rebateValue": {"$ref": "Money", "description": "The value of all tax rebates."}, "savings": {"$ref": "SavingsOverTime", "description": "How much is saved (or not) over the lifetime period."}, "upfrontCost": {"$ref": "Money", "description": "Initial cost after tax incentives: it's the amount that must be paid during first year. Contrast with `out_of_pocket_cost`, which is before tax incentives."}}, "type": "object"}, "DataLayers": {"description": "Information about the solar potential of a region. The actual data are contained in a number of GeoTIFF files covering the requested region, for which this message contains URLs: Each string in the `DataLayers` message contains a URL from which the corresponding GeoTIFF can be fetched. These URLs are valid for a few hours after they've been generated. Most of the GeoTIFF files are at a resolution of 0.1m/pixel, but the monthly flux file is at 0.5m/pixel, and the hourly shade files are at 1m/pixel. If a `pixel_size_meters` value was specified in the `GetDataLayersRequest`, then the minimum resolution in the GeoTIFF files will be that value.", "id": "DataLayers", "properties": {"annualFluxUrl": {"description": "The URL for the annual flux map (annual sunlight on roofs) of the region. Values are kWh/kW/year. This is *unmasked flux*: flux is computed for every location, not just building rooftops. Invalid locations are stored as -9999: locations outside our coverage area will be invalid, and a few locations inside the coverage area, where we were unable to calculate flux, will also be invalid.", "type": "string"}, "dsmUrl": {"description": "The URL for an image of the DSM (Digital Surface Model) of the region. Values are in meters above EGM96 geoid (i.e., sea level). Invalid locations (where we don't have data) are stored as -9999.", "type": "string"}, "hourlyShadeUrls": {"description": "Twelve URLs for hourly shade, corresponding to January...December, in order. Each GeoTIFF will contain 24 bands, corresponding to the 24 hours of the day. Each pixel is a 32 bit integer, corresponding to the (up to) 31 days of that month; a 1 bit means that the corresponding location is able to see the sun at that day, of that hour, of that month. Invalid locations are stored as -9999 (since this is negative, it has bit 31 set, and no valid value could have bit 31 set as that would correspond to the 32nd day of the month). An example may be useful. If you want to know whether a point (at pixel location (x, y)) saw sun at 4pm on the 22nd of June you would: 1. fetch the sixth URL in this list (corresponding to June). 1. look up the 17th channel (corresponding to 4pm). 1. read the 32-bit value at (x, y). 1. read bit 21 of the value (corresponding to the 22nd of the month). 1. if that bit is a 1, then that spot saw the sun at 4pm 22 June. More formally: Given `month` (1-12), `day` (1...month max; February has 28 days) and `hour` (0-23), the shade/sun for that month/day/hour at a position `(x, y)` is the bit ``` (hourly_shade[month - 1])(x, y)[hour] & (1 << (day - 1)) ``` where `(x, y)` is spatial indexing, `[month - 1]` refers to fetching the `month - 1`st URL (indexing from zero), `[hour]` is indexing into the channels, and a final non-zero result means \"sunny\". There are no leap days, and DST doesn't exist (all days are 24 hours long; noon is always \"standard time\" noon).", "items": {"type": "string"}, "type": "array"}, "imageryDate": {"$ref": "Date", "description": "When the source imagery (from which all the other data are derived) in this region was taken. It is necessarily somewhat approximate, as the images may have been taken over more than one day."}, "imageryProcessedDate": {"$ref": "Date", "description": "When processing was completed on this imagery."}, "imageryQuality": {"description": "The quality of the result's imagery.", "enum": ["IMAGERY_QUALITY_UNSPECIFIED", "HIGH", "MEDIUM", "LOW", "BASE"], "enumDescriptions": ["No quality is known.", "Solar data is derived from aerial imagery captured at low-altitude and processed at 0.1 m/pixel.", "Solar data is derived from enhanced aerial imagery captured at high-altitude and processed at 0.25 m/pixel.", "Solar data is derived from enhanced satellite imagery processed at 0.25 m/pixel.", "Solar data is derived from enhanced satellite imagery processed at 0.25 m/pixel. **Note:** This enum is only available if `experiments=EXPANDED_COVERAGE` is set in the request. For more information, see [Expanded Coverage](https://developers.google.com/maps/documentation/solar/expanded-coverage)."], "type": "string"}, "maskUrl": {"description": "The URL for the building mask image: one bit per pixel saying whether that pixel is considered to be part of a rooftop or not.", "type": "string"}, "monthlyFluxUrl": {"description": "The URL for the monthly flux map (sunlight on roofs, broken down by month) of the region. Values are kWh/kW/year. The GeoTIFF pointed to by this URL will contain twelve bands, corresponding to January...December, in order.", "type": "string"}, "rgbUrl": {"description": "The URL for an image of RGB data (aerial photo) of the region.", "type": "string"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "FinancedPurchaseSavings": {"description": "Cost and benefit of using a loan to buy a particular configuration of solar panels with a particular electricity usage.", "id": "FinancedPurchaseSavings", "properties": {"annualLoanPayment": {"$ref": "Money", "description": "Annual loan payments."}, "loanInterestRate": {"description": "The interest rate on loans assumed in this set of calculations.", "format": "float", "type": "number"}, "rebateValue": {"$ref": "Money", "description": "The value of all tax rebates (including Federal Investment Tax Credit (ITC))."}, "savings": {"$ref": "SavingsOverTime", "description": "How much is saved (or not) over the lifetime period."}}, "type": "object"}, "FinancialAnalysis": {"description": "Analysis of the cost and benefits of the optimum solar layout for a particular electric bill size.", "id": "FinancialAnalysis", "properties": {"averageKwhPerMonth": {"description": "How much electricity the house uses in an average month, based on the bill size and the local electricity rates.", "format": "float", "type": "number"}, "cashPurchaseSavings": {"$ref": "CashPurchaseSavings", "description": "Cost and benefit of buying the solar panels with cash."}, "defaultBill": {"description": "Whether this is the bill size selected to be the default bill for the area this building is in. Exactly one `FinancialAnalysis` in `BuildingSolarPotential` should have `default_bill` set.", "type": "boolean"}, "financedPurchaseSavings": {"$ref": "FinancedPurchaseSavings", "description": "Cost and benefit of buying the solar panels by financing the purchase."}, "financialDetails": {"$ref": "FinancialDetails", "description": "Financial information that applies regardless of the financing method used."}, "leasingSavings": {"$ref": "LeasingSavings", "description": "Cost and benefit of leasing the solar panels."}, "monthlyBill": {"$ref": "Money", "description": "The monthly electric bill this analysis assumes."}, "panelConfigIndex": {"description": "Index in solar_panel_configs of the optimum solar layout for this bill size. This can be -1 indicating that there is no layout. In this case, the remaining submessages will be omitted.", "format": "int32", "type": "integer"}}, "type": "object"}, "FinancialDetails": {"description": "Details of a financial analysis. Some of these details are already stored at higher levels (e.g., out of pocket cost). Total money amounts are over a lifetime period defined by the panel_lifetime_years field in SolarPotential. Note: The out of pocket cost of purchasing the panels is given in the out_of_pocket_cost field in CashPurchaseSavings.", "id": "FinancialDetails", "properties": {"costOfElectricityWithoutSolar": {"$ref": "Money", "description": "Total cost of electricity the user would have paid over the lifetime period if they didn't install solar."}, "federalIncentive": {"$ref": "Money", "description": "Amount of money available from federal incentives; this applies if the user buys (with or without a loan) the panels."}, "initialAcKwhPerYear": {"description": "How many AC kWh we think the solar panels will generate in their first year.", "format": "float", "type": "number"}, "lifetimeSrecTotal": {"$ref": "Money", "description": "Amount of money the user will receive from Solar Renewable Energy Credits over the panel lifetime; this applies if the user buys (with or without a loan) the panels."}, "netMeteringAllowed": {"description": "Whether net metering is allowed.", "type": "boolean"}, "percentageExportedToGrid": {"description": "The percentage (0-100) of solar electricity production we assumed was exported to the grid, based on the first quarter of production. This affects the calculations if net metering is not allowed.", "format": "float", "type": "number"}, "remainingLifetimeUtilityBill": {"$ref": "Money", "description": "Utility bill for electricity not produced by solar, for the lifetime of the panels."}, "solarPercentage": {"description": "Percentage (0-100) of the user's power supplied by solar. Valid for the first year but approximately correct for future years.", "format": "float", "type": "number"}, "stateIncentive": {"$ref": "Money", "description": "Amount of money available from state incentives; this applies if the user buys (with or without a loan) the panels."}, "utilityIncentive": {"$ref": "Money", "description": "Amount of money available from utility incentives; this applies if the user buys (with or without a loan) the panels."}}, "type": "object"}, "HttpBody": {"description": "Message that represents an arbitrary HTTP body. It should only be used for payload formats that can't be represented as JSON, such as raw binary or an HTML page. This message can be used both in streaming and non-streaming API methods in the request as well as the response. It can be used as a top-level request field, which is convenient if one wants to extract parameters from either the URL or HTTP template into the request fields and also want access to the raw HTTP body. Example: message GetResourceRequest { // A unique request id. string request_id = 1; // The raw HTTP body is bound to this field. google.api.HttpBody http_body = 2; } service ResourceService { rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); } Example with streaming methods: service CaldavService { rpc GetCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns (stream google.api.HttpBody); } Use of this type only changes how the request and response bodies are handled, all other features will continue to work unchanged.", "id": "HttpBody", "properties": {"contentType": {"description": "The HTTP Content-Type header value specifying the content type of the body.", "type": "string"}, "data": {"description": "The HTTP request/response body as raw binary.", "format": "byte", "type": "string"}, "extensions": {"description": "Application specific response metadata. Must be set in the first response for streaming APIs.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}}, "type": "object"}, "LatLng": {"description": "An object that represents a latitude/longitude pair. This is expressed as a pair of doubles to represent degrees latitude and degrees longitude. Unless specified otherwise, this object must conform to the WGS84 standard. Values must be within normalized ranges.", "id": "LatLng", "properties": {"latitude": {"description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double", "type": "number"}, "longitude": {"description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "format": "double", "type": "number"}}, "type": "object"}, "LatLngBox": {"description": "A bounding box in lat/lng coordinates.", "id": "LatLngBox", "properties": {"ne": {"$ref": "LatLng", "description": "The northeast corner of the box."}, "sw": {"$ref": "LatLng", "description": "The southwest corner of the box."}}, "type": "object"}, "LeasingSavings": {"description": "Cost and benefit of leasing a particular configuration of solar panels with a particular electricity usage.", "id": "LeasingSavings", "properties": {"annualLeasingCost": {"$ref": "Money", "description": "Estimated annual leasing cost."}, "leasesAllowed": {"description": "Whether leases are allowed in this juristiction (leases are not allowed in some states). If this field is false, then the values in this message should probably be ignored.", "type": "boolean"}, "leasesSupported": {"description": "Whether leases are supported in this juristiction by the financial calculation engine. If this field is false, then the values in this message should probably be ignored. This is independent of `leases_allowed`: in some areas leases are allowed, but under conditions that aren't handled by the financial models.", "type": "boolean"}, "savings": {"$ref": "SavingsOverTime", "description": "How much is saved (or not) over the lifetime period."}}, "type": "object"}, "Money": {"description": "Represents an amount of money with its currency type.", "id": "Money", "properties": {"currencyCode": {"description": "The three-letter currency code defined in ISO 4217.", "type": "string"}, "nanos": {"description": "Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.", "format": "int32", "type": "integer"}, "units": {"description": "The whole units of the amount. For example if `currencyCode` is `\"USD\"`, then 1 unit is one US dollar.", "format": "int64", "type": "string"}}, "type": "object"}, "RoofSegmentSizeAndSunshineStats": {"description": "Information about the size and sunniness quantiles of a roof segment.", "id": "RoofSegmentSizeAndSunshineStats", "properties": {"azimuthDegrees": {"description": "Compass direction the roof segment is pointing in. 0 = North, 90 = East, 180 = South. For a \"flat\" roof segment (`pitch_degrees` very near 0), azimuth is not well defined, so for consistency, we define it arbitrarily to be 0 (North).", "format": "float", "type": "number"}, "boundingBox": {"$ref": "LatLngBox", "description": "The bounding box of the roof segment."}, "center": {"$ref": "LatLng", "description": "A point near the center of the roof segment."}, "pitchDegrees": {"description": "Angle of the roof segment relative to the theoretical ground plane. 0 = parallel to the ground, 90 = perpendicular to the ground.", "format": "float", "type": "number"}, "planeHeightAtCenterMeters": {"description": "The height of the roof segment plane, in meters above sea level, at the point designated by `center`. Together with the pitch, azimuth, and center location, this fully defines the roof segment plane.", "format": "float", "type": "number"}, "stats": {"$ref": "SizeAndSunshineStats", "description": "Total size and sunlight quantiles for the roof segment."}}, "type": "object"}, "RoofSegmentSummary": {"description": "Information about a roof segment on the building, with some number of panels placed on it.", "id": "RoofSegmentSummary", "properties": {"azimuthDegrees": {"description": "Compass direction the roof segment is pointing in. 0 = North, 90 = East, 180 = South. For a \"flat\" roof segment (`pitch_degrees` very near 0), azimuth is not well defined, so for consistency, we define it arbitrarily to be 0 (North).", "format": "float", "type": "number"}, "panelsCount": {"description": "The total number of panels on this segment.", "format": "int32", "type": "integer"}, "pitchDegrees": {"description": "Angle of the roof segment relative to the theoretical ground plane. 0 = parallel to the ground, 90 = perpendicular to the ground.", "format": "float", "type": "number"}, "segmentIndex": {"description": "Index in roof_segment_stats of the corresponding `RoofSegmentSizeAndSunshineStats`.", "format": "int32", "type": "integer"}, "yearlyEnergyDcKwh": {"description": "How much sunlight energy this part of the layout captures over the course of a year, in DC kWh, assuming the panels described above.", "format": "float", "type": "number"}}, "type": "object"}, "SavingsOverTime": {"description": "Financial information that's shared between different financing methods.", "id": "SavingsOverTime", "properties": {"financiallyViable": {"description": "Indicates whether this scenario is financially viable. Will be false for scenarios with poor financial viability (e.g., money-losing).", "type": "boolean"}, "presentValueOfSavingsLifetime": {"$ref": "Money", "description": "Using the assumed discount rate, what is the present value of the cumulative lifetime savings?"}, "presentValueOfSavingsYear20": {"$ref": "Money", "description": "Using the assumed discount rate, what is the present value of the cumulative 20-year savings?"}, "savingsLifetime": {"$ref": "Money", "description": "Savings in the entire panel lifetime."}, "savingsYear1": {"$ref": "Money", "description": "Savings in the first year after panel installation."}, "savingsYear20": {"$ref": "Money", "description": "Savings in the first twenty years after panel installation."}}, "type": "object"}, "SizeAndSunshineStats": {"description": "Size and sunniness quantiles of a roof, or part of a roof.", "id": "SizeAndSunshineStats", "properties": {"areaMeters2": {"description": "The area of the roof or roof segment, in m^2. This is the roof area (accounting for tilt), not the ground footprint area.", "format": "float", "type": "number"}, "groundAreaMeters2": {"description": "The ground footprint area covered by the roof or roof segment, in m^2.", "format": "float", "type": "number"}, "sunshineQuantiles": {"description": "Quantiles of the pointwise sunniness across the area. If there are N values here, this represents the (N-1)-iles. For example, if there are 5 values, then they would be the quartiles (min, 25%, 50%, 75%, max). Values are in annual kWh/kW like max_sunshine_hours_per_year.", "items": {"format": "float", "type": "number"}, "type": "array"}}, "type": "object"}, "SolarPanel": {"description": "SolarPanel describes the position, orientation, and production of a single solar panel. See the panel_height_meters, panel_width_meters, and panel_capacity_watts fields in SolarPotential for information on the parameters of the panel.", "id": "SolarPanel", "properties": {"center": {"$ref": "LatLng", "description": "The centre of the panel."}, "orientation": {"description": "The orientation of the panel.", "enum": ["SOLAR_PANEL_ORIENTATION_UNSPECIFIED", "LANDSCAPE", "PORTRAIT"], "enumDescriptions": ["No panel orientation is known.", "A `LANDSCAPE` panel has its long edge perpendicular to the azimuth direction of the roof segment that it is placed on.", "A `PORTRAIT` panel has its long edge parallel to the azimuth direction of the roof segment that it is placed on."], "type": "string"}, "segmentIndex": {"description": "Index in roof_segment_stats of the `RoofSegmentSizeAndSunshineStats` which corresponds to the roof segment that this panel is placed on.", "format": "int32", "type": "integer"}, "yearlyEnergyDcKwh": {"description": "How much sunlight energy this layout captures over the course of a year, in DC kWh.", "format": "float", "type": "number"}}, "type": "object"}, "SolarPanelConfig": {"description": "SolarPanelConfig describes a particular placement of solar panels on the roof.", "id": "SolarPanelConfig", "properties": {"panelsCount": {"description": "Total number of panels. Note that this is redundant to (the sum of) the corresponding fields in roof_segment_summaries.", "format": "int32", "type": "integer"}, "roofSegmentSummaries": {"description": "Information about the production of each roof segment that is carrying at least one panel in this layout. `roof_segment_summaries[i]` describes the i-th roof segment, including its size, expected production and orientation.", "items": {"$ref": "RoofSegmentSummary"}, "type": "array"}, "yearlyEnergyDcKwh": {"description": "How much sunlight energy this layout captures over the course of a year, in DC kWh, assuming the panels described above.", "format": "float", "type": "number"}}, "type": "object"}, "SolarPotential": {"description": "Information about the solar potential of a building. A number of fields in this are defined in terms of \"panels\". The fields panel_capacity_watts, panel_height_meters, and panel_width_meters describe the parameters of the model of panel used in these calculations.", "id": "SolarPotential", "properties": {"buildingStats": {"$ref": "SizeAndSunshineStats", "description": "Size and sunlight quantiles for the entire building, including parts of the roof that were not assigned to some roof segment. Because the orientations of these parts are not well characterised, the roof area estimate is unreliable, but the ground area estimate is reliable. It may be that a more reliable whole building roof area can be obtained by scaling the roof area from whole_roof_stats by the ratio of the ground areas of `building_stats` and `whole_roof_stats`."}, "carbonOffsetFactorKgPerMwh": {"description": "Equivalent amount of CO2 produced per MWh of grid electricity. This is a measure of the carbon intensity of grid electricity displaced by solar electricity.", "format": "float", "type": "number"}, "financialAnalyses": {"description": "A FinancialAnalysis gives the savings from going solar assuming a given monthly bill and a given electricity provider. They are in order of increasing order of monthly bill amount. This field will be empty for buildings in areas for which the Solar API does not have enough information to perform financial computations.", "items": {"$ref": "FinancialAnalysis"}, "type": "array"}, "maxArrayAreaMeters2": {"description": "Size, in square meters, of the maximum array.", "format": "float", "type": "number"}, "maxArrayPanelsCount": {"description": "Size of the maximum array - that is, the maximum number of panels that can fit on the roof.", "format": "int32", "type": "integer"}, "maxSunshineHoursPerYear": {"description": "Maximum number of sunshine hours received per year, by any point on the roof. Sunshine hours are a measure of the total amount of insolation (energy) received per year. 1 sunshine hour = 1 kWh per kW (where kW refers to kW of capacity under Standard Testing Conditions).", "format": "float", "type": "number"}, "panelCapacityWatts": {"description": "Capacity, in watts, of the panel used in the calculations.", "format": "float", "type": "number"}, "panelHeightMeters": {"description": "Height, in meters in portrait orientation, of the panel used in the calculations.", "format": "float", "type": "number"}, "panelLifetimeYears": {"description": "The expected lifetime, in years, of the solar panels. This is used in the financial calculations.", "format": "int32", "type": "integer"}, "panelWidthMeters": {"description": "Width, in meters in portrait orientation, of the panel used in the calculations.", "format": "float", "type": "number"}, "roofSegmentStats": {"description": "Size and sunlight quantiles for each roof segment.", "items": {"$ref": "RoofSegmentSizeAndSunshineStats"}, "type": "array"}, "solarPanelConfigs": {"description": "Each SolarPanelConfig describes a different arrangement of solar panels on the roof. They are in order of increasing number of panels. The `SolarPanelConfig` with panels_count=N is based on the first N panels in the `solar_panels` list. This field is only populated if at least 4 panels can fit on a roof.", "items": {"$ref": "SolarPanelConfig"}, "type": "array"}, "solarPanels": {"description": "Each SolarPanel describes a single solar panel. They are listed in the order that the panel layout algorithm placed this. This is usually, though not always, in decreasing order of annual energy production.", "items": {"$ref": "SolarPanel"}, "type": "array"}, "wholeRoofStats": {"$ref": "SizeAndSunshineStats", "description": "Total size and sunlight quantiles for the part of the roof that was assigned to some roof segment. Despite the name, this may not include the entire building. See building_stats."}}, "type": "object"}}, "servicePath": "", "title": "Solar API", "version": "v1", "version_module": true}