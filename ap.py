import json
import random
import os # <--- MISSING IMPORT ADDED HERE

# --- Configuration ---
NUM_TWEETS_TO_GENERATE = 15000 # You can reduce this for faster testing if needed, e.g., to 1500
OUTPUT_FILE = "tweet_datasets/various_smartphones_tweets.json" 

# --- Smartphone Models & Brands ---
BRANDS = ["Apple", "Samsung", "Google", "OnePlus", "Xiaomi", "Oppo", "Vivo", "Realme", "Sony"]
APPLE_MODELS = ["iPhone 15", "iPhone 15 Pro", "iPhone 14", "iPhone SE", "iPhone 13 Pro Max"]
SAMSUNG_MODELS = ["Galaxy S24 Ultra", "Galaxy S23", "Galaxy Z Fold 5", "Galaxy A54", "Galaxy S22 Plus"]
GOOGLE_MODELS = ["Pixel 8 Pro", "Pixel 7a", "Pixel Fold", "Pixel 6"]
ONEPLUS_MODELS = ["OnePlus 12", "OnePlus Open", "OnePlus Nord 3", "OnePlus 11"]
XIAOMI_MODELS = ["Xiaomi 14 Ultra", "Xiaomi 13T", "Redmi Note 12 Pro", "Poco F5"]

ALL_MODELS = APPLE_MODELS + SAMSUNG_MODELS + GOOGLE_MODELS + ONEPLUS_MODELS + XIAOMI_MODELS

# --- Aspects & Keywords (simplified for generation) ---
ASPECTS = {
    "camera": ["camera", "photos", "video quality", "zoom", "low-light performance"],
    "battery": ["battery life", "charging speed", "power", "duration"],
    "screen": ["display", "screen quality", "refresh rate", "brightness", "Dynamic Island"],
    "performance": ["performance", "speed", "gaming experience", "chipset", "A17 Bionic", "Snapdragon 8 Gen 3"],
    "design": ["design", "build quality", "feel", "colors", "Action Button", "titanium frame"],
    "software": ["software", "iOS", "Android", "UI", "updates", "bloatware"],
    "price": ["price", "cost", "value", "expensive", "affordable"],
    "overall": ["overall experience", "general feel", "recommendation"]
}

# --- Sentiment Templates ---
POSITIVE_TEMPLATES = [
    "Just got the {model}! The {aspect} is absolutely amazing. So impressed!",
    "Loving my new {model}. Its {aspect} is a huge step up from my old phone.",
    "The {model} is fantastic. {aspect_keyword} are top-notch. Highly recommend!",
    "Can't get enough of the {model}! Especially the {aspect}. Worth every penny.",
    "If you're looking for a phone with great {aspect}, the {model} is the one.",
    "The {model}'s {aspect} is a game changer. So smooth and responsive.",
    "Finally upgraded to {model}, and the {aspect_keyword} did not disappoint!",
    "Blown away by the {aspect} on the {model}. {brand} really nailed it this time."
]

NEGATIVE_TEMPLATES = [
    "Really disappointed with the {model}. The {aspect} is worse than I expected.",
    "My {model} has terrible {aspect}. Regretting this purchase.",
    "The {aspect} on the {model} is a letdown. Not worth the {price_keyword}.",
    "Having so many issues with the {model}'s {aspect}. Frustrating!",
    "Wish the {model} had a better {aspect}. It's a major drawback for me.",
    "The {aspect_keyword} on my {model} are just not good enough for the price.",
    "Not happy with the {model}. The {aspect} makes it hard to recommend.",
    "{brand} needs to fix the {aspect} issues on the {model}."
]

NEUTRAL_QUESTION_TEMPLATES = [
    "Thinking of buying the {model}. How is the {aspect} in real-world usage?",
    "Anyone have experience with the {model}'s {aspect_keyword}?",
    "Just unboxed the {model}. Looking forward to testing out its {aspect}.",
    "What are your thoughts on the {model} and its {aspect} compared to competitors?",
    "Saw the announcement for the {model}. The {aspect} specs seem interesting.",
    "Is the {model}'s {aspect} a significant improvement over the previous generation?",
    "Considering the {model} for its {aspect}. Any long-term reviews out there?",
    "How does the {model} from {brand} handle {aspect_keyword}?"
]

# --- Helper Functions ---
def get_random_model_and_brand():
    model = random.choice(ALL_MODELS)
    brand = ""
    if model in APPLE_MODELS: brand = "Apple"
    elif model in SAMSUNG_MODELS: brand = "Samsung"
    elif model in GOOGLE_MODELS: brand = "Google"
    elif model in ONEPLUS_MODELS: brand = "OnePlus"
    elif model in XIAOMI_MODELS: brand = "Xiaomi"
    else: brand = random.choice(BRANDS)
    return model, brand

def get_random_aspect_and_keyword():
    aspect_name = random.choice(list(ASPECTS.keys()))
    keyword = random.choice(ASPECTS[aspect_name])
    return aspect_name, keyword

# --- Main Generation Logic ---
def generate_tweets(num_tweets):
    tweets = []
    for i in range(num_tweets):
        model, brand = get_random_model_and_brand()
        aspect_name, aspect_keyword = get_random_aspect_and_keyword()
        price_keyword = random.choice(ASPECTS["price"]) 

        sentiment_type = random.choices(
            ["positive", "negative", "neutral"], 
            weights=[0.45, 0.30, 0.25], 
            k=1
        )[0]

        tweet_text = ""
        if sentiment_type == "positive":
            template = random.choice(POSITIVE_TEMPLATES)
            tweet_text = template.format(model=model, aspect=aspect_name, aspect_keyword=aspect_keyword, brand=brand)
        elif sentiment_type == "negative":
            template = random.choice(NEGATIVE_TEMPLATES)
            tweet_text = template.format(model=model, aspect=aspect_name, aspect_keyword=aspect_keyword, price_keyword=price_keyword, brand=brand)
        else: 
            template = random.choice(NEUTRAL_QUESTION_TEMPLATES)
            tweet_text = template.format(model=model, aspect=aspect_name, aspect_keyword=aspect_keyword, brand=brand)
        
        if random.random() < 0.1: tweet_text += " " + random.choice(["IMO.", "Just saying.", "Thoughts?", "What do you think?", "#"+model.replace(" ","")])
        if random.random() < 0.05: tweet_text = tweet_text.upper() 
        if random.random() < 0.05 and len(tweet_text.split()) > 5: 
            if not tweet_text.endswith("?"): tweet_text = tweet_text.rstrip(".") + "?"

        tweets.append(tweet_text)
        if (i + 1) % 1000 == 0: # Print progress every 1000 tweets
            print(f"Generated {i+1} tweets...")
            
    return tweets

if __name__ == "__main__":
    print(f"Starting generation of {NUM_TWEETS_TO_GENERATE} mock tweets...")
    generated_tweets = generate_tweets(NUM_TWEETS_TO_GENERATE)
    
    # Ensure the output directory exists
    # This was the part causing the error in your log
    output_dir = os.path.dirname(OUTPUT_FILE) 
    if output_dir and not os.path.exists(output_dir): # Check if output_dir is not an empty string
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")

    try:
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(generated_tweets, f, ensure_ascii=False, indent=2) 
        print(f"Successfully saved {len(generated_tweets)} tweets to {OUTPUT_FILE}")
    except Exception as e:
        print(f"Error saving tweets to file: {e}")