<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
    <title>Sign Up - Smartphone Analyzer</title>
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
    
    <!-- All styles are inlined here, consistent with the login page -->
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0; /* Allow vertical scrolling for taller forms */
            position: relative;
            overflow-x: hidden;
        }
        .bg-animation {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            overflow: hidden; z-index: 0;
        }
        .floating-shape {
            position: absolute; background: rgba(255, 255, 255, 0.1);
            border-radius: 50%; animation: float 6s ease-in-out infinite;
        }
        .shape-1 { width: 80px; height: 80px; top: 15%; left: 10%; animation-delay: 0s; }
        .shape-2 { width: 120px; height: 120px; top: 70%; right: 15%; animation-delay: 2s; }
        .shape-3 { width: 60px; height: 60px; bottom: 10%; left: 20%; animation-delay: 4s; }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        .register-container {
            background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px);
            border-radius: 24px; padding: 40px; width: 100%; max-width: 420px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative; z-index: 1; animation: slideUp 0.8s ease-out; margin: 40px 0;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .logo-section { text-align: center; margin-bottom: 32px; }
        .logo-icon {
            width: 64px; height: 64px; background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 16px; display: inline-flex; align-items: center;
            justify-content: center; margin-bottom: 16px;
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }
        .logo-icon svg { width: 32px; height: 32px; fill: white; }
        .app-title {
            font-size: 28px; font-weight: 700; color: #1a202c; margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
        }
        .app-subtitle { font-size: 16px; color: #718096; font-weight: 400; }
        .form-group { margin-bottom: 20px; position: relative; }
        .form-label { display: block; font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 8px; }
        .form-input {
            width: 100%; padding: 14px 20px; border: 2px solid #e5e7eb;
            border-radius: 12px; font-size: 16px; transition: all 0.3s ease;
            background: #fafafa; color: #374151;
        }
        .form-input:focus {
            outline: none; border-color: #667eea; background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .password-toggle {
            position: absolute; right: 16px; top: 50%; transform: translateY(-50%);
            background: none; border: none; cursor: pointer; color: #6b7280;
            padding: 4px; border-radius: 4px; transition: color 0.2s ease;
        }
        .text-center { text-align: center; }
        .login-link { text-align: center; font-size: 14px; color: #6b7280; margin-top: 32px; }
        .login-link a { color: #667eea; text-decoration: none; font-weight: 600; transition: color 0.2s ease; }
        .login-link a:hover { color: #764ba2; text-decoration: underline; }
        
        /* Button Styles */
        .register-btn {
            width: 100%; padding: 16px; background: linear-gradient(135deg, #667eea, #764ba2);
            color: white; border: none; border-radius: 12px; font-size: 16px;
            font-weight: 600; cursor: pointer; transition: all 0.3s ease;
            position: relative; overflow: hidden; margin-top: 12px;
        }
        .register-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4); }
        .register-btn.loading .btn-text { opacity: 0; }
        .register-btn.loading .loading-spinner { opacity: 1; }
        .loading-spinner {
            position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 20px; height: 20px; border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white; border-radius: 50%;
            animation: spin 1s linear infinite; opacity: 0; transition: opacity 0.3s ease;
        }
        @keyframes spin { 0% { transform: translate(-50%, -50%) rotate(0deg); } 100% { transform: translate(-50%, -50%) rotate(360deg); } }

        /* Error Styles */
        .field-error { color: #dc2626; font-size: 12px; margin-top: 6px; display: block; }
        .form-input.error { border-color: #dc2626; background-color: #fef2f2; }
    </style>
</head>
<body>
    <div class="bg-animation">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
    </div>

    <div class="register-container">
        <div class="logo-section">
            <div class="logo-icon">
                <svg viewBox="0 0 24 24">
                     <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
                </svg>
            </div>
            <h1 class="app-title">Create Your Account</h1>
            <p class="app-subtitle">Join us to start analyzing smartphone sentiment.</p>
        </div>

        <!-- REGISTRATION FORM - Integrated with Flask-WTForms -->
        <form method="POST" action="{{ url_for('register') }}" id="registerForm" novalidate>
            {{ form.hidden_tag() }} <!-- CSRF Protection -->

            <div class="form-group">
                {{ form.username.label(class="form-label") }}
                {{ form.username(
                    class="form-input" + (" error" if form.username.errors else ""), 
                    placeholder="e.g., john_doe"
                ) }}
                {% if form.username.errors %}
                    {% for error in form.username.errors %}
                        <div class="field-error">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="form-group">
                {{ form.email.label(class="form-label") }}
                {{ form.email(
                    class="form-input" + (" error" if form.email.errors else ""), 
                    placeholder="<EMAIL>"
                ) }}
                {% if form.email.errors %}
                    {% for error in form.email.errors %}
                        <div class="field-error">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="form-group">
                {{ form.password.label(class="form-label") }}
                <div style="position: relative;">
                    {{ form.password(
                        class="form-input" + (" error" if form.password.errors else ""), 
                        placeholder="Choose a strong password"
                    ) }}
                    <button type="button" class="password-toggle" data-target="password">👁️</button>
                </div>
                {% if form.password.errors %}
                    {% for error in form.password.errors %}
                        <div class="field-error">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>
            
            <div class="form-group">
                {{ form.confirm_password.label(class="form-label") }}
                <div style="position: relative;">
                    {{ form.confirm_password(
                        class="form-input" + (" error" if form.confirm_password.errors else ""), 
                        placeholder="Confirm your password"
                    ) }}
                    <button type="button" class="password-toggle" data-target="confirm_password">👁️</button>
                </div>
                {% if form.confirm_password.errors %}
                    {% for error in form.confirm_password.errors %}
                        <div class="field-error">{{ error }}</div>
                    {% endfor %}
                {% endif %}
            </div>

            <div class="text-center">
                <button type="submit" class="register-btn" id="registerBtn">
                    <span class="btn-text">Create Account</span>
                    <div class="loading-spinner"></div>
                </button>
            </div>
        </form>

        <div class="login-link">
            Already have an account? <a href="{{ url_for('login') }}">Sign In</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('registerForm');
            const registerBtn = document.getElementById('registerBtn');
            const passwordToggles = document.querySelectorAll('.password-toggle');

            // Handle multiple password visibility toggles
            passwordToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const passwordInput = document.getElementById(targetId);
                    
                    if (passwordInput) {
                        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                        passwordInput.setAttribute('type', type);
                        this.textContent = type === 'password' ? '👁️' : '🙈';
                    }
                });
            });
            
            // On form submission, show the loading spinner
            if (registerForm) {
                registerForm.addEventListener('submit', function(e) {
                    if (registerBtn) {
                        registerBtn.classList.add('loading');
                    }
                });
            }
        });
    </script>
</body>
</html>