<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Sign Up - Product Analyzer</title>
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />
  <style>
    /* Enhanced Sign-Up Page Styles */
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Inter', sans-serif;
    }

    .page-header {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%), url('{{ url_for('static', filename='assets/img/curved-images/curved14.jpg') }}') !important;
      background-size: cover !important;
      background-position: center !important;
      border-radius: 25px !important;
      margin: 20px !important;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }

    .mask {
      display: none !important;
    }

    h1 {
      font-weight: 800 !important;
      font-size: 3rem !important;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .text-lead {
      font-size: 1.2rem !important;
      font-weight: 500 !important;
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }

    .card {
      background: rgba(255, 255, 255, 0.95) !important;
      backdrop-filter: blur(20px);
      border: none !important;
      border-radius: 25px !important;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-10px);
      box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4) !important;
    }

    .card-header {
      background: transparent !important;
      border: none !important;
      padding: 30px 30px 0 30px !important;
    }

    .card-body {
      padding: 30px !important;
    }

    h5 {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-weight: 800 !important;
      font-size: 2rem !important;
      margin-bottom: 10px;
    }

    .form-control {
      border: 2px solid #e9ecef !important;
      border-radius: 15px !important;
      padding: 15px 20px !important;
      font-size: 16px !important;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.9) !important;
    }

    .form-control:focus {
      border-color: #667eea !important;
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.3) !important;
      transform: translateY(-2px);
      background: white !important;
    }

    .form-label {
      font-weight: 600 !important;
      color: #2d3748 !important;
      margin-bottom: 8px !important;
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      border: none !important;
      border-radius: 25px !important;
      padding: 15px 30px !important;
      font-weight: 600 !important;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
      width: 100%;
    }

    .btn-primary:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6) !important;
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
    }

    .alert {
      border: none !important;
      border-radius: 15px !important;
      padding: 15px 20px !important;
      margin: 20px 0 !important;
      font-weight: 500;
    }

    .alert-danger {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
      color: white !important;
    }

    .alert-success {
      background: linear-gradient(135deg, #51cf66 0%, #40c057 100%) !important;
      color: white !important;
    }

    .text-danger {
      color: #ff6b6b !important;
      font-weight: 500 !important;
    }

    p {
      color: #6c757d !important;
      font-weight: 500 !important;
    }

    a {
      color: #667eea !important;
      text-decoration: none !important;
      font-weight: 600 !important;
      transition: all 0.3s ease;
    }

    a:hover {
      color: #764ba2 !important;
      text-decoration: underline !important;
    }

    /* Animated Background Elements */
    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx=".5" cy=".5" r=".5"><stop offset="0" stop-color="%23ffffff" stop-opacity=".1"/><stop offset="1" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="20" cy="20" r="10" fill="url(%23a)"/><circle cx="80" cy="80" r="15" fill="url(%23a)"/><circle cx="40" cy="70" r="8" fill="url(%23a)"/><circle cx="90" cy="30" r="12" fill="url(%23a)"/><circle cx="10" cy="90" r="6" fill="url(%23a)"/></svg>');
      opacity: 0.3;
      z-index: -1;
      animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }

    /* Form Animation */
    .card {
      animation: slideInUp 0.8s ease-out;
    }

    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(50px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Header Animation */
    .page-header {
      animation: slideInDown 0.8s ease-out;
    }

    @keyframes slideInDown {
      from {
        opacity: 0;
        transform: translateY(-50px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>
<body class="">
  <main class="main-content  mt-0">
    <section class="min-vh-100 mb-8">
      <div class="page-header align-items-start min-vh-50 pt-5 pb-11 m-3 border-radius-lg" style="background-image: url('{{ url_for('static', filename='assets/img/curved-images/curved14.jpg') }}');">
        <span class="mask bg-gradient-dark opacity-6"></span>
        <div class="container">
          <div class="row justify-content-center">
            <div class="col-lg-5 text-center mx-auto">
              <h1 class="text-white mb-2 mt-5">Welcome!</h1>
              <p class="text-lead text-white">Create a new account to start analyzing product sentiment.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="container">
        <div class="row mt-lg-n10 mt-md-n11 mt-n10">
          <div class="col-xl-4 col-lg-5 col-md-7 mx-auto">
            <div class="card z-index-0">
              <div class="card-header text-center pt-4">
                <h5>Register</h5>
              </div>
              <div class="card-body">
                <form role="form" method="POST" action="{{ url_for('register') }}">
                  {{ form.hidden_tag() }} <!-- Important for CSRF protection -->
                  
                  <div class="mb-3">
                    {{ form.username.label(class="form-label") }}
                    {{ form.username(class="form-control", placeholder="Username") }}
                    {% if form.username.errors %}
                        {% for error in form.username.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    {% endif %}
                  </div>

                  <div class="mb-3">
                    {{ form.email.label(class="form-label") }}
                    {{ form.email(class="form-control", placeholder="Email") }}
                    {% if form.email.errors %}
                        {% for error in form.email.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    {% endif %}
                  </div>

                  <div class="mb-3">
                    {{ form.password.label(class="form-label") }}
                    {{ form.password(class="form-control", placeholder="Password") }}
                    {% if form.password.errors %}
                        {% for error in form.password.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    {% endif %}
                  </div>
                  
                  <div class="mb-3">
                    {{ form.confirm_password.label(class="form-label") }}
                    {{ form.confirm_password(class="form-control", placeholder="Confirm Password") }}
                    {% if form.confirm_password.errors %}
                        {% for error in form.confirm_password.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    {% endif %}
                  </div>

                  <div class="text-center">
                    {{ form.submit(class="btn bg-gradient-dark w-100 my-4 mb-2") }}
                  </div>
                  <p class="text-sm mt-3 mb-0">Already have an account? <a href="{{ url_for('login') }}" class="text-dark font-weight-bolder">Sign in</a></p>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
</body>
</html>