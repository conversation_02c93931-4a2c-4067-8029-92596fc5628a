<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Sign Up - Product Analyzer</title>
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />
</head>
<body class="">
  <main class="main-content  mt-0">
    <section class="min-vh-100 mb-8">
      <div class="page-header align-items-start min-vh-50 pt-5 pb-11 m-3 border-radius-lg" style="background-image: url('{{ url_for('static', filename='assets/img/curved-images/curved14.jpg') }}');">
        <span class="mask bg-gradient-dark opacity-6"></span>
        <div class="container">
          <div class="row justify-content-center">
            <div class="col-lg-5 text-center mx-auto">
              <h1 class="text-white mb-2 mt-5">Welcome!</h1>
              <p class="text-lead text-white">Create a new account to start analyzing product sentiment.</p>
            </div>
          </div>
        </div>
      </div>
      <div class="container">
        <div class="row mt-lg-n10 mt-md-n11 mt-n10">
          <div class="col-xl-4 col-lg-5 col-md-7 mx-auto">
            <div class="card z-index-0">
              <div class="card-header text-center pt-4">
                <h5>Register</h5>
              </div>
              <div class="card-body">
                <form role="form" method="POST" action="{{ url_for('register') }}">
                  {{ form.hidden_tag() }} <!-- Important for CSRF protection -->
                  
                  <div class="mb-3">
                    {{ form.username.label(class="form-label") }}
                    {{ form.username(class="form-control", placeholder="Username") }}
                    {% if form.username.errors %}
                        {% for error in form.username.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    {% endif %}
                  </div>

                  <div class="mb-3">
                    {{ form.email.label(class="form-label") }}
                    {{ form.email(class="form-control", placeholder="Email") }}
                    {% if form.email.errors %}
                        {% for error in form.email.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    {% endif %}
                  </div>

                  <div class="mb-3">
                    {{ form.password.label(class="form-label") }}
                    {{ form.password(class="form-control", placeholder="Password") }}
                    {% if form.password.errors %}
                        {% for error in form.password.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    {% endif %}
                  </div>
                  
                  <div class="mb-3">
                    {{ form.confirm_password.label(class="form-label") }}
                    {{ form.confirm_password(class="form-control", placeholder="Confirm Password") }}
                    {% if form.confirm_password.errors %}
                        {% for error in form.confirm_password.errors %}
                            <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    {% endif %}
                  </div>

                  <div class="text-center">
                    {{ form.submit(class="btn bg-gradient-dark w-100 my-4 mb-2") }}
                  </div>
                  <p class="text-sm mt-3 mb-0">Already have an account? <a href="{{ url_for('login') }}" class="text-dark font-weight-bolder">Sign in</a></p>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
</body>
</html>