{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "", "baseUrl": "https://merchantapi.googleapis.com/", "batchPath": "batch", "canonicalName": "Merchant", "description": "Programmatically manage your Merchant Center Accounts.", "discoveryVersion": "v1", "documentationLink": "https://developers.devsite.corp.google.com/merchant/api", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "merchantapi:notifications_v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://merchantapi.mtls.googleapis.com/", "name": "merchantapi", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"resources": {"notificationsubscriptions": {"methods": {"create": {"description": "Creates a notification subscription for a business. For standalone or subaccounts accounts, the business can create a subscription for self. For MCAs, the business can create a subscription for all managed accounts or for a specific subaccount. We will allow the following types of notification subscriptions to exist together (per business as a subscriber per event type): 1. Subscription for all managed accounts + subscription for self. 2. Multiple \"partial\" subscriptions for managed accounts + subscription for self. we will not allow (per business as a subscriber per event type): 1. Multiple self subscriptions. 2. Multiple \"all managed accounts\" subscriptions. 3. \"All managed accounts\" subscription and partial subscriptions at the same time. 4. Multiple partial subscriptions for the same target account.", "flatPath": "notifications/v1beta/accounts/{accountsId}/notificationsubscriptions", "httpMethod": "POST", "id": "merchantapi.accounts.notificationsubscriptions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The merchant account that owns the new notification subscription. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "notifications/v1beta/{+parent}/notificationsubscriptions", "request": {"$ref": "NotificationSubscription"}, "response": {"$ref": "NotificationSubscription"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a notification subscription for a merchant.", "flatPath": "notifications/v1beta/accounts/{accountsId}/notificationsubscriptions/{notificationsubscriptionsId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.notificationsubscriptions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the notification subscription to be deleted.", "location": "path", "pattern": "^accounts/[^/]+/notificationsubscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "notifications/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Gets notification subscriptions for an account.", "flatPath": "notifications/v1beta/accounts/{accountsId}/notificationsubscriptions/{notificationsubscriptionsId}", "httpMethod": "GET", "id": "merchantapi.accounts.notificationsubscriptions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The `name` of the notification subscription.", "location": "path", "pattern": "^accounts/[^/]+/notificationsubscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "notifications/v1beta/{+name}", "response": {"$ref": "NotificationSubscription"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Gets all the notification subscriptions for a merchant.", "flatPath": "notifications/v1beta/accounts/{accountsId}/notificationsubscriptions", "httpMethod": "GET", "id": "merchantapi.accounts.notificationsubscriptions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of notification subscriptions to return in a page. The default value for `page_size` is 100. The maximum value is `200`. Values above `200` will be coerced to `200`.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Token (if provided) to retrieve the subsequent page. All other parameters must match the original call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The merchant account who owns the notification subscriptions. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "notifications/v1beta/{+parent}/notificationsubscriptions", "response": {"$ref": "ListNotificationSubscriptionsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates an existing notification subscription for a merchant.", "flatPath": "notifications/v1beta/accounts/{accountsId}/notificationsubscriptions/{notificationsubscriptionsId}", "httpMethod": "PATCH", "id": "merchantapi.accounts.notificationsubscriptions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The `name` of the notification configuration. Generated by the Content API upon creation of a new `NotificationSubscription`. The `account` represents the merchant ID of the merchant that owns the configuration. Format: `accounts/{account}/notificationsubscriptions/{notification_subscription}`", "location": "path", "pattern": "^accounts/[^/]+/notificationsubscriptions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "List of fields being updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "notifications/v1beta/{+name}", "request": {"$ref": "NotificationSubscription"}, "response": {"$ref": "NotificationSubscription"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}}, "revision": "********", "rootUrl": "https://merchantapi.googleapis.com/", "schemas": {"Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ListNotificationSubscriptionsResponse": {"description": "Response message for the ListNotificationSubscription method.", "id": "ListNotificationSubscriptionsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "notificationSubscriptions": {"description": "The list of notification subscriptions requested by the merchant.", "items": {"$ref": "NotificationSubscription"}, "type": "array"}}, "type": "object"}, "NotificationSubscription": {"description": "Represents a notification subscription owned by a Merchant account.", "id": "NotificationSubscription", "properties": {"allManagedAccounts": {"description": "If this value is true, the requesting account is notified of the specified event for all managed accounts (can be subaccounts or other linked accounts) including newly added accounts on a daily basis.", "type": "boolean"}, "callBackUri": {"description": "URL to be used to push the notification to the merchant.", "type": "string"}, "name": {"description": "Output only. The `name` of the notification configuration. Generated by the Content API upon creation of a new `NotificationSubscription`. The `account` represents the merchant ID of the merchant that owns the configuration. Format: `accounts/{account}/notificationsubscriptions/{notification_subscription}`", "readOnly": true, "type": "string"}, "registeredEvent": {"description": "The event that the merchant wants to be notified about.", "enum": ["NOTIFICATION_EVENT_TYPE_UNSPECIFIED", "PRODUCT_STATUS_CHANGE"], "enumDescriptions": ["Notifications event type is unspecified.", "Notification of product status changes, for example when product becomes disapproved."], "type": "string"}, "targetAccount": {"description": "The `name` of the account you want to receive notifications for. Format: `accounts/{account}`", "type": "string"}}, "type": "object"}, "ProductChange": {"description": "The change that happened to the product including old value, new value, country code as the region code and reporting context.", "id": "ProductChange", "properties": {"newValue": {"description": "The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "oldValue": {"description": "The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "regionCode": {"description": "Countries that have the change (if applicable). Represented in the ISO 3166 format.", "type": "string"}, "reportingContext": {"description": "Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/********).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}}, "type": "object"}, "ProductStatusChangeMessage": {"description": "The message that the merchant will receive to notify about product status change event", "id": "ProductStatusChangeMessage", "properties": {"account": {"description": "The target account that owns the entity that changed. Format : `accounts/{merchant_id}`", "type": "string"}, "attribute": {"description": "The attribute in the resource that changed, in this case it will be always `Status`.", "enum": ["ATTRIBUTE_UNSPECIFIED", "STATUS"], "enumDescriptions": ["Unspecified attribute", "Status of the changed entity"], "type": "string"}, "changes": {"description": "A message to describe the change that happened to the product", "items": {"$ref": "ProductChange"}, "type": "array"}, "eventTime": {"description": "The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.", "format": "google-datetime", "type": "string"}, "expirationTime": {"description": "Optional. The product expiration time. This field will not bet set if the notification is sent for a product deletion event.", "format": "google-datetime", "type": "string"}, "managingAccount": {"description": "The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id}`", "type": "string"}, "resource": {"description": "The product name. Format: `accounts/{account}/products/{product}`", "type": "string"}, "resourceId": {"description": "The product id.", "type": "string"}, "resourceType": {"description": "The resource that changed, in this case it will always be `Product`.", "enum": ["RESOURCE_UNSPECIFIED", "PRODUCT"], "enumDescriptions": ["Unspecified resource", "Resource type : product"], "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Merchant API", "version": "notifications_v1beta", "version_module": true}