<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Product Comparison</title>
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    .chart-container { padding: 10px; border: 1px solid #eee; border-radius: 8px; background-color: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .product-image-container { max-width: 250px; max-height: 250px; margin: 15px auto; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa; }
    .product-image-container img { width: 100%; height: 100%; object-fit: contain; }
    .specifications-display { background-color: #f8f9fa; border-radius: .5rem; padding: 15px; font-size: 0.9em; height: 200px; overflow-y: auto; border: 1px solid #e9ecef; }
    .specifications-display p, .specifications-display li { margin-bottom: 0.5rem; white-space: pre-wrap; }
    .loading-overlay { position: fixed; display: flex; align-items: center; justify-content: center; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.8); z-index: 2000; }
  </style>
</head>
<body class="g-sidenav-show bg-gray-100">
  <aside class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-3" id="sidenav-main">
    <div class="sidenav-header">
      <i class="fas fa-times p-3 cursor-pointer text-secondary opacity-5 position-absolute end-0 top-0 d-none d-xl-none" aria-hidden="true" id="iconSidenav"></i>
      <a class="navbar-brand m-0" href="{{ url_for('index') }}">
        <img src="{{ url_for('static', filename='assets/img/logo-ct-dark.png') }}" class="navbar-brand-img h-100" alt="main_logo">
        <span class="ms-1 font-weight-bold">Product Analyzer</span>
      </a>
    </div>
    <hr class="horizontal dark mt-0">
    <div class="collapse navbar-collapse w-auto" id="sidenav-collapse-main">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('index') }}">
              <div class="icon icon-shape icon-sm shadow border-radius-md bg-white text-center me-2 d-flex align-items-center justify-content-center">
                <i class="fas fa-magnifying-glass-chart text-secondary"></i>
              </div>
              <span class="nav-link-text ms-1">Single Analyzer</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="#">
              <div class="icon icon-shape icon-sm shadow border-radius-md bg-white text-center me-2 d-flex align-items-center justify-content-center">
                <i class="fas fa-balance-scale text-primary"></i>
              </div>
              <span class="nav-link-text ms-1">Compare</span>
            </a>
          </li>
        </ul>
      </div>
  </aside>

  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
    <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" navbar-scroll="true">
        <div class="container-fluid py-1 px-3">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
                <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="{{ url_for('index') }}">Analyzer</a></li>
                <li class="breadcrumb-item text-sm text-dark active" aria-current="page">Comparison</li>
              </ol>
              <h6 class="font-weight-bolder mb-0">Product Comparison</h6>
            </nav>
            <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
              <ul class="navbar-nav justify-content-end ms-auto">
                <li class="nav-item d-flex align-items-center">
                  <span class="nav-link text-body font-weight-bold px-0"><i class="fa fa-user me-sm-1"></i><span class="d-sm-inline d-none">{{ username }}</span></span>
                </li>
                <li class="nav-item d-flex align-items-center ms-3">
                  <a href="{{ url_for('logout') }}" class="nav-link text-body font-weight-bold px-0"><i class="fa fa-sign-out-alt me-sm-1"></i><span class="d-sm-inline d-none">Logout</span></a>
                </li>
              </ul>
            </div>
        </div>
    </nav>

    <div id="loadingOverlay" class="loading-overlay">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading Comparison...</span>
        </div>
    </div>

    <div class="container-fluid py-4" id="comparisonResults" style="display: none;">
      <div class="row">
        <!-- Product 1 Column -->
        <div class="col-lg-6">
            <h4 class="text-center mb-3" id="product1NameHeader">{{ product1 }}</h4>
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div id="product1ImageContainer" class="product-image-container">
                        <img id="product1Image" src="#" alt="Product 1 Image" style="display:none;">
                        <p id="noImage1Text">No image found.</p>
                    </div>
                </div>
            </div>
            <!-- NEW: Specifications Snippet Card -->
            <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Specifications Snippet</h6></div>
                <div class="card-body">
                    <div id="product1SpecSnippet" class="specifications-display">
                        <p style="color: #6c757d;">Fetching specs...</p>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Overall Sentiment (<span id="tweets1Count">0</span> tweets)</h6></div>
                <div class="card-body">
                    <div class="chart-container" style="height:250px;"><canvas id="overall1Chart"></canvas></div>
                </div>
            </div>
             <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Aspect Sentiment</h6></div>
                <div class="card-body"><div class="chart-container" style="height:300px;"><canvas id="aspect1Chart"></canvas></div></div>
            </div>
        </div>

        <!-- Product 2 Column -->
        <div class="col-lg-6">
            <h4 class="text-center mb-3" id="product2NameHeader">{{ product2 }}</h4>
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div id="product2ImageContainer" class="product-image-container">
                        <img id="product2Image" src="#" alt="Product 2 Image" style="display:none;">
                        <p id="noImage2Text">No image found.</p>
                    </div>
                </div>
            </div>
            <!-- NEW: Specifications Snippet Card -->
            <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Specifications Snippet</h6></div>
                <div class="card-body">
                    <div id="product2SpecSnippet" class="specifications-display">
                        <p style="color: #6c757d;">Fetching specs...</p>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Overall Sentiment (<span id="tweets2Count">0</span> tweets)</h6></div>
                <div class="card-body">
                    <div class="chart-container" style="height:250px;"><canvas id="overall2Chart"></canvas></div>
                </div>
            </div>
             <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Aspect Sentiment</h6></div>
                <div class="card-body"><div class="chart-container" style="height:300px;"><canvas id="aspect2Chart"></canvas></div></div>
            </div>
        </div>
      </div>
    </div>
  </main>
  
  <script src="{{ url_for('static', filename='assets/js/core/popper.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/core/bootstrap.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/compare_app.js') }}"></script>
</body>
</html>