<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Product Comparison</title>
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    /* ===== ENHANCED COMPARE PAGE STYLES ===== */

    /* Enhanced Chart Containers */
    .chart-container {
      padding: 20px;
      border: none;
      border-radius: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .chart-container:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 40px rgba(0,0,0,0.3);
    }

    /* Enhanced Product Image Container */
    .product-image-container {
      max-width: 240px;
      max-height: 240px;
      margin: 20px auto;
      border: none;
      border-radius: 20px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      transition: transform 0.3s ease;
    }
    .product-image-container:hover {
      transform: scale(1.05);
    }
    .product-image-container img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      border-radius: 15px;
    }

    /* Enhanced Specifications Display */
    .specifications-display {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 15px;
      padding: 20px;
      font-size: 0.9em;
      height: 220px;
      overflow-y: auto;
      border: none;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
    }
    .specifications-display:hover {
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .specifications-display p, .specifications-display li {
      margin-bottom: 0.8rem;
      white-space: pre-wrap;
      color: #2d3748;
      font-weight: 500;
    }

    /* Enhanced Loading Overlay */
    .loading-overlay {
      position: fixed;
      display: flex;
      align-items: center;
      justify-content: center;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
      backdrop-filter: blur(10px);
      z-index: 2000;
    }

    /* Enhanced Cards */
    .card {
      border: none !important;
      border-radius: 20px !important;
      box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
      transition: all 0.3s ease;
      overflow: hidden;
    }
    .card:hover {
      transform: translateY(-8px);
      box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
    }
    .card-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      color: white !important;
      border: none !important;
      padding: 20px !important;
      font-weight: 600;
    }
    .card-body {
      padding: 25px !important;
    }

    /* Enhanced Form Styling */
    .form-control {
      border: 2px solid #e9ecef !important;
      border-radius: 15px !important;
      padding: 15px 20px !important;
      font-size: 16px !important;
      transition: all 0.3s ease;
      background: rgba(255,255,255,0.9) !important;
    }
    .form-control:focus {
      border-color: #667eea !important;
      box-shadow: 0 0 20px rgba(102, 126, 234, 0.3) !important;
      transform: translateY(-2px);
    }

    /* Enhanced Buttons */
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      border: none !important;
      border-radius: 25px !important;
      padding: 12px 30px !important;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    .btn-primary:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
    }

    /* Enhanced Background */
    .main-content {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
      min-height: 100vh;
    }

    /* Enhanced Sidebar */
    .sidenav {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
    }

    /* Enhanced Navigation */
    .navbar-main {
      background: rgba(255,255,255,0.95) !important;
      backdrop-filter: blur(10px);
      box-shadow: 0 5px 20px rgba(0,0,0,0.1) !important;
    }

    /* Enhanced Typography */
    h1, h2, h3, h4, h5, h6 {
      font-weight: 700 !important;
      color: #2d3748 !important;
    }

    /* Animated Elements */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .card {
      animation: fadeInUp 0.6s ease-out;
    }

    /* Enhanced Loading Spinner */
    .spinner-border {
      width: 3rem !important;
      height: 3rem !important;
      border-width: 4px !important;
      color: white !important;
    }

    /* Enhanced Alert Messages */
    .alert {
      border: none !important;
      border-radius: 15px !important;
      padding: 15px 20px !important;
      font-weight: 500;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    /* Enhanced FontAwesome Icons */
    .fas, .far, .fab {
      transition: all 0.3s ease;
    }

    .nav-link:hover .fas,
    .nav-link:hover .far,
    .nav-link:hover .fab {
      transform: scale(1.1);
    }

    /* Comparison specific styles */
    .comparison-section {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 30px;
      margin: 20px 0;
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .vs-divider {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      font-weight: 800;
      color: #667eea;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body class="g-sidenav-show bg-gray-100">
  <aside class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-3" id="sidenav-main">
    <div class="sidenav-header">
      <i class="fas fa-times p-3 cursor-pointer text-secondary opacity-5 position-absolute end-0 top-0 d-none d-xl-none" aria-hidden="true" id="iconSidenav"></i>
      <a class="navbar-brand m-0" href="{{ url_for('index') }}">
        <img src="{{ url_for('static', filename='assets/img/logo-ct-dark.png') }}" class="navbar-brand-img h-100" alt="main_logo">
        <span class="ms-1 font-weight-bold">Product Analyzer</span>
      </a>
    </div>
    <hr class="horizontal dark mt-0">
    <div class="collapse navbar-collapse w-auto" id="sidenav-collapse-main">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="{{ url_for('index') }}">
              <div class="icon icon-shape icon-sm shadow border-radius-md bg-white text-center me-2 d-flex align-items-center justify-content-center">
                <i class="fas fa-magnifying-glass-chart text-secondary"></i>
              </div>
              <span class="nav-link-text ms-1">Single Analyzer</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="#">
              <div class="icon icon-shape icon-sm shadow border-radius-md bg-white text-center me-2 d-flex align-items-center justify-content-center">
                <i class="fas fa-balance-scale text-primary"></i>
              </div>
              <span class="nav-link-text ms-1">Compare</span>
            </a>
          </li>
        </ul>
      </div>
  </aside>

  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
    <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" navbar-scroll="true">
        <div class="container-fluid py-1 px-3">
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
                <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="{{ url_for('index') }}">Analyzer</a></li>
                <li class="breadcrumb-item text-sm text-dark active" aria-current="page">Comparison</li>
              </ol>
              <h6 class="font-weight-bolder mb-0">Product Comparison</h6>
            </nav>
            <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
              <ul class="navbar-nav justify-content-end ms-auto">
                <li class="nav-item d-flex align-items-center">
                  <span class="nav-link text-body font-weight-bold px-0"><i class="fa fa-user me-sm-1"></i><span class="d-sm-inline d-none">{{ username }}</span></span>
                </li>
                <li class="nav-item d-flex align-items-center ms-3">
                  <a href="{{ url_for('logout') }}" class="nav-link text-body font-weight-bold px-0"><i class="fa fa-sign-out-alt me-sm-1"></i><span class="d-sm-inline d-none">Logout</span></a>
                </li>
              </ul>
            </div>
        </div>
    </nav>

    <div id="loadingOverlay" class="loading-overlay">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Loading Comparison...</span>
        </div>
    </div>

    <div class="container-fluid py-4" id="comparisonResults" style="display: none;">
      <div class="row">
        <!-- Product 1 Column -->
        <div class="col-lg-6">
            <h4 class="text-center mb-3" id="product1NameHeader">{{ product1 }}</h4>
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div id="product1ImageContainer" class="product-image-container">
                        <img id="product1Image" src="#" alt="Product 1 Image" style="display:none;">
                        <p id="noImage1Text">No image found.</p>
                    </div>
                </div>
            </div>
            <!-- NEW: Specifications Snippet Card -->
            <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Specifications Snippet</h6></div>
                <div class="card-body">
                    <div id="product1SpecSnippet" class="specifications-display">
                        <p style="color: #6c757d;">Fetching specs...</p>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Overall Sentiment (<span id="tweets1Count">0</span> tweets)</h6></div>
                <div class="card-body">
                    <div class="chart-container" style="height:250px;"><canvas id="overall1Chart"></canvas></div>
                </div>
            </div>
             <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Aspect Sentiment</h6></div>
                <div class="card-body"><div class="chart-container" style="height:300px;"><canvas id="aspect1Chart"></canvas></div></div>
            </div>
        </div>

        <!-- Product 2 Column -->
        <div class="col-lg-6">
            <h4 class="text-center mb-3" id="product2NameHeader">{{ product2 }}</h4>
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div id="product2ImageContainer" class="product-image-container">
                        <img id="product2Image" src="#" alt="Product 2 Image" style="display:none;">
                        <p id="noImage2Text">No image found.</p>
                    </div>
                </div>
            </div>
            <!-- NEW: Specifications Snippet Card -->
            <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Specifications Snippet</h6></div>
                <div class="card-body">
                    <div id="product2SpecSnippet" class="specifications-display">
                        <p style="color: #6c757d;">Fetching specs...</p>
                    </div>
                </div>
            </div>
            <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Overall Sentiment (<span id="tweets2Count">0</span> tweets)</h6></div>
                <div class="card-body">
                    <div class="chart-container" style="height:250px;"><canvas id="overall2Chart"></canvas></div>
                </div>
            </div>
             <div class="card mb-4">
                <div class="card-header"><h6 class="mb-0">Aspect Sentiment</h6></div>
                <div class="card-body"><div class="chart-container" style="height:300px;"><canvas id="aspect2Chart"></canvas></div></div>
            </div>
        </div>
      </div>
    </div>
  </main>
  
  <script src="{{ url_for('static', filename='assets/js/core/popper.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/core/bootstrap.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/compare_app.js') }}"></script>
</body>
</html>