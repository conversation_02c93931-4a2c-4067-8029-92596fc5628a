from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from flask_bcrypt import B<PERSON>rypt
from datetime import datetime

db = SQLAlchemy()
bcrypt = Bcrypt()

class User(db.Model, UserMixin):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)

    def __init__(self, username, email, password):
        self.username = username
        self.email = email
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')

    def check_password(self, password):
        return bcrypt.check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

# --- NEW CLASS ADDED ---
class SearchHistory(db.Model):
    __tablename__ = 'search_history'
    
    id = db.Column(db.Integer, primary_key=True)
    product_name = db.Column(db.String(255), nullable=False)
    search_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    
    # Foreign key to link to the User table
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Relationship to easily access the User object from a SearchHistory instance
    user = db.relationship('User', backref=db.backref('search_history', lazy=True, cascade="all, delete-orphan"))

    def __repr__(self):
        return f'<SearchHistory {self.product_name} by User {self.user_id}>'