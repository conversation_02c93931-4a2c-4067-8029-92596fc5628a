<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <link rel="icon" type="image/png" href="{{ url_for('static', filename='assets/img/favicon.png') }}">
  <title>Product Sentiment Analyzer</title>

  <!-- Fonts and Icons -->
  <link href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700,800" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link id="pagestyle" href="{{ url_for('static', filename='assets/css/soft-ui-dashboard.css') }}?v=1.1.0" rel="stylesheet" />

  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }
    .card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .card-header h6 {
      font-weight: 700;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    .form-control:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    }
    .btn-primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: none;
      color: #fff;
    }
    .btn-primary:hover {
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    .nav-link.text-body,
    .nav-link.text-body .fa,
    .nav-link.text-body span {
      color: #fff !important;
    }
    .nav-link.active .icon {
      background: linear-gradient(135deg, #667eea, #764ba2) !important;
    }
    .nav-link.active {
      background-color: #667eea !important;
      color: #fff !important;
      border-radius: 12px;
    }
    .navbar-main {
      background: transparent;
    }
    .breadcrumb-item a,
    .breadcrumb-item.active,
    .font-weight-bolder.mb-0 {
      color: white !important;
    }
    .nav-link.active .icon {
      background: linear-gradient(135deg, #4e73df, #1cc88a);
    }
    .nav-link.active {
      background-color: #4e73df;
      color: #fff;
    }
    .nav-link.active .icon i {
      color: white;
    }

    /* Chart and Product Image Styles */
    .chart-container {
      padding: 10px;
      border: 1px solid #eee;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0,0,0,.1);
    }
    .product-image-container {
      max-width: 250px;
      max-height: 250px;
      margin: 15px auto;
      border: 1px solid #ddd;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f8f9fa;
      cursor: pointer;
    }
    .product-image-container img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .specifications-display {
      background-color: #f8f9fa;
      border-radius: .5rem;
      padding: 15px;
      font-size: .9em;
      height: 250px;
      overflow-y: auto;
      border: 1px solid #e9ecef;
    }
    .specifications-display p,
    .specifications-display li {
      margin-bottom: .5rem;
      white-space: pre-wrap;
    }

    /* Image Zoom Modal */
    .image-zoom-modal {
      display: none;
      position: fixed;
      z-index: 1055;
      padding-top: 50px;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0,0,0,.8);
    }
    .image-zoom-modal-content {
      margin: auto;
      display: block;
      width: 80%;
      max-width: 700px;
      max-height: 80vh;
      animation-name: zoom;
      animation-duration: .4s;
    }
    @keyframes zoom {
      from { transform: scale(0) }
      to { transform: scale(1) }
    }
    .image-zoom-close {
      position: absolute;
      top: 15px;
      right: 35px;
      color: #f1f1f1;
      font-size: 40px;
      font-weight: 700;
      transition: .3s;
      cursor: pointer;
    }
    .image-zoom-close:focus,
    .image-zoom-close:hover {
      color: #bbb;
      text-decoration: none;
    }
  </style>
</head>
<body class="g-sidenav-show bg-gray-100">
  <aside class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-3" id="sidenav-main">
    <div class="sidenav-header">
      <a class="navbar-brand m-0" href="{{ url_for('index') }}">
        <img src="{{ url_for('static', filename='assets/img/logo-ct-dark.png') }}" class="navbar-brand-img h-100" alt="main_logo">
        <span class="ms-1 font-weight-bold">Product Analyzer</span>
      </a>
    </div>
    <hr class="horizontal dark mt-0">
    <div class="collapse navbar-collapse w-auto" id="sidenav-collapse-main">
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link active text-white" href="{{ url_for('index') }}">
            <div class="icon icon-shape icon-sm shadow border-radius-md bg-gradient-primary text-center me-2 d-flex align-items-center justify-content-center">
              <i class="fas fa-search text-white"></i>
            </div>
            <span class="nav-link-text ms-1">New Search</span>
          </a>
        </li>
      </ul>
    </div>
  </aside>

  <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
    <nav class="navbar navbar-main navbar-expand-lg px-0 mx-4 shadow-none border-radius-xl" id="navbarBlur" navbar-scroll="true">
      <div class="container-fluid py-1 px-3">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
            <li class="breadcrumb-item text-sm"><a class="opacity-5 text-white" href="{{ url_for('index') }}">Search</a></li>
            <li class="breadcrumb-item text-sm text-white active" aria-current="page">Results</li>
          </ol>
          <h6 class="font-weight-bolder mb-0 text-white">Results for {{ product1_name }}{% if product2_name %} vs {{ product2_name }}{% endif %}</h6>
        </nav>
        <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
          <ul class="navbar-nav justify-content-end ms-auto">
            <li class="nav-item d-flex align-items-center">
              <span class="nav-link text-body font-weight-bold px-0">
                <i class="fa fa-user me-sm-1"></i>
                <span class="d-sm-inline d-none">{{ username }}</span>
              </span>
            </li>
            <li class="nav-item d-flex align-items-center ms-3">
              <a href="{{ url_for('logout') }}" class="nav-link text-body font-weight-bold px-0">
                <i class="fa fa-sign-out-alt me-sm-1"></i>
                <span class="d-sm-inline d-none">Logout</span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <div class="container-fluid py-4">
      <!-- Main conditional check: are we in comparison mode? -->
      {% if results2 %}
          <!-- COMPARISON VIEW (TWO COLUMNS) -->
          <div class="row">
              <!-- Product 1 Column -->
              <div class="col-lg-6 mb-4">
                  {% with results = results1, product_name = product1_name, id_suffix = '1' %}
                      {% include 'partials/_result_column.html' %}
                  {% endwith %}
              </div>
              <!-- Product 2 Column -->
              <div class="col-lg-6 mb-4">
                  {% with results = results2, product_name = product2_name, id_suffix = '2' %}
                      {% include 'partials/_result_column.html' %}
                  {% endwith %}
              </div>
          </div>
      {% else %}
          <!-- SINGLE PRODUCT VIEW (FULL WIDTH) -->
          <div class="row">
              <div class="col-12">
                  {% with results = results1, product_name = product1_name, id_suffix = '1' %}
                      {% include 'partials/_result_column.html' %}
                  {% endwith %}
              </div>
          </div>
      {% endif %}
    </div>
  </main>

  <!-- Image Zoom Modal -->
  <div id="imageZoomModal" class="image-zoom-modal">
    <span class="image-zoom-close">×</span>
    <img class="image-zoom-modal-content" id="zoomedImage">
  </div>

  <!-- Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    const results1Data = {{ results1 | tojson | safe }};
    const results2Data = {{ results2 | tojson | safe if results2 else 'null' }};
  </script>
  <script src="{{ url_for('static', filename='assets/js/sentiment_app.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/core/popper.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/core/bootstrap.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/js/soft-ui-dashboard.min.js') }}?v=1.1.0"></script>
</body>
</html>
