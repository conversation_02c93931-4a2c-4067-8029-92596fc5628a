document.addEventListener('DOMContentLoaded', function () {
    const sentimentForm = document.getElementById('sentimentForm');
    const resultsSection = document.getElementById('resultsSection');
    const productImage = document.getElementById('productImage');
    const noImageText = document.getElementById('noImageText');
    const overallSentimentChartCtx = document.getElementById('overallSentimentChart').getContext('2d');
    const aspectChartsContainer = document.getElementById('aspectChartsContainer');
    const noAspectsMessage = document.getElementById('noAspectsMessage');
    const analyzedProductName = document.getElementById('analyzedProductName');
    const tweetsCountElement = document.getElementById('tweetsCount');
    const sampleTweetsList = document.getElementById('sampleTweetsList');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const apiErrorMessageElement = document.getElementById('apiErrorMessage');

    let overallChartInstance = null;
    const aspectChartInstances = {};

    sentimentForm.addEventListener('submit', async function (e) {
        e.preventDefault();
        loadingSpinner.style.display = 'inline-block';
        resultsSection.style.display = 'none';
        apiErrorMessageElement.style.display = 'none';
        apiErrorMessageElement.textContent = '';
        noImageText.textContent = 'Searching for image...'; // Reset image placeholder text

        const productName = document.getElementById('productName').value;
        // productImageUrl is no longer taken from form

        try {
            const response = await fetch('/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ product_name: productName }), // Only send product_name
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            if (data.error_message) { // Display non-fatal errors/info from backend (e.g., Twitter API issues)
                apiErrorMessageElement.textContent = `Info: ${data.error_message}`;
                apiErrorMessageElement.style.display = 'block';
            }

            displayResults(data);

        } catch (error) {
            console.error('Error fetching sentiment analysis:', error);
            apiErrorMessageElement.textContent = `Error: ${error.message}`;
            apiErrorMessageElement.style.display = 'block';
            resultsSection.style.display = 'none';
        } finally {
            loadingSpinner.style.display = 'none';
        }
    });

    function displayResults(data) {
        resultsSection.style.display = 'block';
        analyzedProductName.textContent = `Analysis for: ${data.product_name}`;

        // Scroll to results section smoothly after a short delay
        setTimeout(() => {
            resultsSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }, 200); // Small delay to ensure the section is visible first

        if (data.tweets_count > 0) {
            tweetsCountElement.textContent = `Based on ${data.tweets_count} tweets.`;
        } else if (data.error_message && data.error_message.includes("Twitter")) { // Be more specific for tweet errors
            tweetsCountElement.textContent = "Could not fetch tweets (see info message).";
        } else {
            tweetsCountElement.textContent = "No tweets found or analyzed.";
        }

        // Product Image - now fetched by backend
        if (data.product_image_url) {
            productImage.src = data.product_image_url;
            productImage.style.display = 'block';
            noImageText.style.display = 'none';
            productImage.onerror = () => {
                productImage.style.display = 'none';
                noImageText.style.display = 'block';
                noImageText.textContent = 'Image could not be loaded or was not found.';
            };
        } else {
            productImage.style.display = 'none';
            noImageText.style.display = 'block';
            noImageText.textContent = 'No product image found.';
        }

        // Overall Sentiment Chart
        if (overallChartInstance) {
            overallChartInstance.destroy();
        }
        const totalOverallSentiments = data.overall_sentiment.positive + data.overall_sentiment.negative + data.overall_sentiment.neutral;
        if (totalOverallSentiments > 0) {
           overallSentimentChartCtx.canvas.parentElement.style.display = 'block';
            overallChartInstance = new Chart(overallSentimentChartCtx, {
                type: 'pie', // Or 'doughnut'
                data: {
                    labels: ['Positive', 'Negative', 'Neutral'],
                    datasets: [{
                        label: 'Overall Sentiment',
                        data: [
                            data.overall_sentiment.positive,
                            data.overall_sentiment.negative,
                            data.overall_sentiment.neutral
                        ],
                        backgroundColor: ['rgba(75, 192, 192, 0.7)', 'rgba(255, 99, 132, 0.7)', 'rgba(201, 203, 207, 0.7)'],
                        borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(201, 203, 207, 1)'],
                        borderWidth: 1
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { position: 'top' }, title: { display: true, text: 'Overall Product Sentiment' }}}
            });
        } else {
             overallSentimentChartCtx.canvas.parentElement.style.display = 'none';
        }

        // Aspect-Based Sentiment Charts
        aspectChartsContainer.innerHTML = '';
        Object.keys(aspectChartInstances).forEach(key => {
            if(aspectChartInstances[key]) aspectChartInstances[key].destroy();
        });

        const aspectsPresent = data.aspect_sentiments && Object.keys(data.aspect_sentiments).length > 0;
        if (aspectsPresent) {
            noAspectsMessage.style.display = 'none';
            aspectChartsContainer.style.display = 'flex'; // Ensure container is visible
            for (const aspect in data.aspect_sentiments) {
                const aspectData = data.aspect_sentiments[aspect];
                if (aspectData.mentions === 0) continue;

                const colDiv = document.createElement('div');
                colDiv.className = 'col-md-6 col-lg-4 mb-4';
                const chartContainerDiv = document.createElement('div');
                chartContainerDiv.className = 'chart-container';
                chartContainerDiv.style.height = '300px';
                const canvas = document.createElement('canvas');
                canvas.id = `aspectChart-${aspect}`;
                chartContainerDiv.appendChild(canvas);
                colDiv.appendChild(chartContainerDiv);
                aspectChartsContainer.appendChild(colDiv);

                aspectChartInstances[aspect] = new Chart(canvas.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: ['Positive', 'Negative', 'Neutral'],
                        datasets: [{
                            label: `${aspect} (Mentions: ${aspectData.mentions})`,
                            data: [aspectData.positive, aspectData.negative, aspectData.neutral],
                            backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(255, 99, 132, 0.6)', 'rgba(201, 203, 207, 0.6)'],
                            borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(201, 203, 207, 1)'],
                            borderWidth: 1
                        }]
                    },
                    options: { responsive: true, maintainAspectRatio: false, indexAxis: 'y', scales: { x: { beginAtZero: true, title: { display: true, text: '# Mentions' }}}, plugins: { legend: { display: false }, title: { display: true, text: `Sentiment: ${aspect.charAt(0).toUpperCase() + aspect.slice(1)}` }}}
                });
            }
        } else {
            noAspectsMessage.style.display = 'block';
            aspectChartsContainer.style.display = 'none'; // Hide container if no aspects
        }

        // Sample Tweets
        sampleTweetsList.innerHTML = '';
        if (data.sample_tweets && data.sample_tweets.length > 0) {
            data.sample_tweets.forEach(tweet => {
                const li = document.createElement('li');
                li.className = 'list-group-item';
                let sentimentClass = '';
                if (tweet.sentiment === 'positive') sentimentClass = 'text-success';
                else if (tweet.sentiment === 'negative') sentimentClass = 'text-danger';
                else sentimentClass = 'text-secondary';

                li.innerHTML = `<span class="fw-bold ${sentimentClass}">[${tweet.sentiment.toUpperCase()}]</span>: ${tweet.text}`;
                sampleTweetsList.appendChild(li);
            });
        } else {
            const li = document.createElement('li');
            li.className = 'list-group-item';
            li.textContent = 'No sample tweets to display.';
            sampleTweetsList.appendChild(li);
        }
    }
});