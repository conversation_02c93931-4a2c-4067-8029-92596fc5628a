{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud services and see the email address of your Google Account"}, "https://www.googleapis.com/auth/firebase": {"description": "View and administer all your Firebase data and settings"}, "https://www.googleapis.com/auth/firebase.readonly": {"description": "View all your Firebase data and settings"}}}}, "basePath": "", "baseUrl": "https://firebasehosting.googleapis.com/", "batchPath": "batch", "canonicalName": "Firebase Hosting", "description": "The Firebase Hosting REST API enables programmatic and customizable management and deployments to your Firebase-hosted sites. Use this REST API to create and manage channels and sites as well as to deploy new or updated hosting configurations and content files.", "discoveryVersion": "v1", "documentationLink": "https://firebase.google.com/docs/hosting/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "firebasehosting:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://firebasehosting.mtls.googleapis.com/", "name": "firebasehosting", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/operations/{operationsId}", "httpMethod": "GET", "id": "firebasehosting.projects.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}, "sites": {"methods": {"create": {"description": "Creates a new Hosting Site in the specified parent Firebase project. Note that Hosting sites can take several minutes to propagate through Firebase systems.", "flatPath": "v1beta1/projects/{projectsId}/sites", "httpMethod": "POST", "id": "firebasehosting.projects.sites.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The Firebase project in which to create a Hosting site, in the format: projects/PROJECT_IDENTIFIER Refer to the `Site` [`name`](../projects#Site.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "siteId": {"description": "Required. Immutable. A globally unique identifier for the Hosting site. This identifier is used to construct the Firebase-provisioned subdomains for the site, so it must also be a valid domain name label.", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates that the site_id is available and that the request would succeed, returning the expected resulting site or error.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/sites", "request": {"$ref": "Site"}, "response": {"$ref": "Site"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Deletes the specified Hosting Site from the specified parent Firebase project.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}", "httpMethod": "DELETE", "id": "firebasehosting.projects.sites.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the Hosting site, in the format: projects/PROJECT_IDENTIFIER/sites/SITE_ID Refer to the `Site` [`name`](../projects#Site.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified Hosting Site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}", "httpMethod": "GET", "id": "firebasehosting.projects.sites.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the Hosting site, in the format: projects/PROJECT_IDENTIFIER/sites/SITE_ID Refer to the `Site` [`name`](../projects#Site.FIELDS.name) field for details about PROJECT_IDENTIFIER values. Since a SITE_ID is a globally unique identifier, you can also use the unique sub-collection resource access pattern, in the format: projects/-/sites/SITE_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Site"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "getConfig": {"description": "Gets the Hosting metadata for a specific site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/config", "httpMethod": "GET", "id": "firebasehosting.projects.sites.getConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The site for which to get the SiteConfig, in the format: sites/ site-name/config", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/config$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "SiteConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists each Hosting Site associated with the specified parent Firebase project.", "flatPath": "v1beta1/projects/{projectsId}/sites", "httpMethod": "GET", "id": "firebasehosting.projects.sites.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of sites to return. The service may return a lower number if fewer sites exist than this maximum number. If unspecified, defaults to 40.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token from a previous call to `ListSites` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Firebase project for which to list sites, in the format: projects/PROJECT_IDENTIFIER Refer to the `Site` [`name`](../projects#Site.FIELDS.name) field for details about PROJECT_IDENTIFIER values.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/sites", "response": {"$ref": "ListSitesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": "Updates attributes of the specified Hosting Site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}", "httpMethod": "PATCH", "id": "firebasehosting.projects.sites.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The fully-qualified resource name of the Hosting site, in the format: projects/PROJECT_IDENTIFIER/sites/SITE_ID PROJECT_IDENTIFIER: the Firebase project's [`ProjectNumber`](https://firebase.google.com/docs/reference/firebase-management/rest/v1beta1/projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](https://firebase.google.com/docs/reference/firebase-management/rest/v1beta1/projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510).", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "A set of field names from your Site that you want to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Site"}, "response": {"$ref": "Site"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "updateConfig": {"description": "Sets the Hosting metadata for a specific site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/config", "httpMethod": "PATCH", "id": "firebasehosting.projects.sites.updateConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The site for which to update the SiteConfig, in the format: sites/ site-name/config", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/config$", "required": true, "type": "string"}, "updateMask": {"description": "A set of field names from your [site configuration](../sites.SiteConfig) that you want to update. A field will be overwritten if, and only if, it's in the mask. If a mask is not provided then a default mask of only [`max_versions`](../sites.SiteConfig.max_versions) will be used.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "SiteConfig"}, "response": {"$ref": "SiteConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}, "resources": {"channels": {"methods": {"create": {"description": "Creates a new channel in the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/channels", "httpMethod": "POST", "id": "firebasehosting.projects.sites.channels.create", "parameterOrder": ["parent"], "parameters": {"channelId": {"description": "Required. Immutable. A unique ID within the site that identifies the channel.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site in which to create this channel, in the format: sites/ SITE_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/channels", "request": {"$ref": "Channel"}, "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Deletes the specified channel of the specified site. The `live` channel cannot be deleted.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/channels/{channelsId}", "httpMethod": "DELETE", "id": "firebasehosting.projects.sites.channels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the channel, in the format: sites/SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Retrieves information for the specified channel of the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/channels/{channelsId}", "httpMethod": "GET", "id": "firebasehosting.projects.sites.channels.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the channel, in the format: sites/SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the channels for the specified site. All sites have a default `live` channel.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/channels", "httpMethod": "GET", "id": "firebasehosting.projects.sites.channels.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of channels to return. The service may return a lower number if fewer channels exist than this maximum number. If unspecified, defaults to 10. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `ListChannels` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site for which to list channels, in the format: sites/SITE_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/channels", "response": {"$ref": "ListChannelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": "Updates information for the specified channel of the specified site. Implicitly creates the channel if it doesn't already exist.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/channels/{channelsId}", "httpMethod": "PATCH", "id": "firebasehosting.projects.sites.channels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The fully-qualified resource name for the channel, in the format: sites/ SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "A comma-separated list of fields to be updated in this request.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Channel"}, "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}, "resources": {"releases": {"methods": {"create": {"description": "Creates a new release, which makes the content of the specified version actively display on the appropriate URL(s).", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/channels/{channelsId}/releases", "httpMethod": "POST", "id": "firebasehosting.projects.sites.channels.releases.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The site or channel to which the release belongs, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}, "versionName": {"description": " The unique identifier for a version, in the format: sites/SITE_ID/versions/ VERSION_ID The SITE_ID in this version identifier must match the SITE_ID in the `parent` parameter. This query parameter must be empty if the `type` field in the request body is `SITE_DISABLE`.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/releases", "request": {"$ref": "Release"}, "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified release for a site or channel. When used to get a release for a site, this can get releases for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/channels/{channelsId}/releases/{releasesId}", "httpMethod": "GET", "id": "firebasehosting.projects.sites.channels.releases.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the Hosting release, in either of the following formats: - sites/SITE_ID/channels/CHANNEL_ID/releases/RELEASE_ID - sites/SITE_ID/releases/RELEASE_ID ", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/channels/[^/]+/releases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the releases that have been created for the specified site or channel. When used to list releases for a site, this list includes releases for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/channels/{channelsId}/releases", "httpMethod": "GET", "id": "firebasehosting.projects.sites.channels.releases.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of releases to return. The service may return a lower number if fewer releases exist than this maximum number. If unspecified, defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `releases.list` or `channels.releases.list` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site or channel for which to list releases, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID ", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/releases", "response": {"$ref": "ListReleasesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}}}, "customDomains": {"methods": {"create": {"description": "Creates a `CustomDomain`.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/customDomains", "httpMethod": "POST", "id": "firebasehosting.projects.sites.customDomains.create", "parameterOrder": ["parent"], "parameters": {"customDomainId": {"description": "Required. The ID of the `CustomDomain`, which is the domain name you'd like to use with Firebase Hosting.", "location": "query", "type": "string"}, "parent": {"description": "Required. The custom domain's parent, specifically a Firebase Hosting `Site`.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "If true, Hosting validates that it's possible to complete your request but doesn't actually create a new `CustomDomain`.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/customDomains", "request": {"$ref": "CustomDomain"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Deletes the specified `CustomDomain`.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/customDomains/{customDomainsId}", "httpMethod": "DELETE", "id": "firebasehosting.projects.sites.customDomains.delete", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If true, the request succeeds even if the `CustomDomain` doesn't exist.", "location": "query", "type": "boolean"}, "etag": {"description": "A tag that represents the state of the `CustomDomain` as you know it. If present, the supplied tag must match the current value on your `CustomDomain`, or the request fails.", "location": "query", "type": "string"}, "name": {"description": "Required. The name of the `CustomDomain` to delete.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/customDomains/[^/]+$", "required": true, "type": "string"}, "validateOnly": {"description": "If true, Hosting validates that it's possible to complete your request but doesn't actually delete the `CustomDomain`.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified `CustomDomain`.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/customDomains/{customDomainsId}", "httpMethod": "GET", "id": "firebasehosting.projects.sites.customDomains.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `CustomDomain` to get.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/customDomains/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "CustomDomain"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists each `CustomDomain` associated with the specified parent Hosting site. Returns `CustomDomain`s in a consistent, but undefined, order to facilitate pagination.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/customDomains", "httpMethod": "GET", "id": "firebasehosting.projects.sites.customDomains.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The max number of `CustomDomain` entities to return in a request. Defaults to 10.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `ListCustomDomains` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The Firebase Hosting `Site` with `CustomDomain` entities you'd like to list.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "If true, the request returns soft-deleted `CustomDomain`s that haven't been fully-deleted yet. To restore deleted `CustomDomain`s, make an `UndeleteCustomDomain` request.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+parent}/customDomains", "response": {"$ref": "ListCustomDomainsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": "Updates the specified `CustomDomain`.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/customDomains/{customDomainsId}", "httpMethod": "PATCH", "id": "firebasehosting.projects.sites.customDomains.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "If true, Hosting creates the `CustomDomain` if it doesn't already exist.", "location": "query", "type": "boolean"}, "name": {"description": "Output only. The fully-qualified name of the `CustomDomain`.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/customDomains/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The set of field names from your `CustomDomain` that you want to update. A field will be overwritten if, and only if, it's in the mask. If you don't provide a mask, Hosting updates the entire `CustomDomain`.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "If true, Hosting validates that it's possible to complete your request but doesn't actually create or update the `CustomDomain`.", "location": "query", "type": "boolean"}}, "path": "v1beta1/{+name}", "request": {"$ref": "CustomDomain"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "undelete": {"description": "Undeletes the specified `CustomDomain` if it has been soft-deleted. Hosting retains soft-deleted custom domains for around 30 days before permanently deleting them.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/customDomains/{customDomainsId}:undelete", "httpMethod": "POST", "id": "firebasehosting.projects.sites.customDomains.undelete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the `CustomDomain` to delete.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/customDomains/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:undelete", "request": {"$ref": "UndeleteCustomDomainRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}, "resources": {"operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/customDomains/{customDomainsId}/operations/{operationsId}", "httpMethod": "GET", "id": "firebasehosting.projects.sites.customDomains.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/customDomains/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists operations that match the specified filter in the request.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/customDomains/{customDomainsId}/operations", "httpMethod": "GET", "id": "firebasehosting.projects.sites.customDomains.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/customDomains/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}}}, "domains": {"methods": {"create": {"description": "Creates a domain mapping on the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/domains", "httpMethod": "POST", "id": "firebasehosting.projects.sites.domains.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent to create the domain association for, in the format: sites/site-name", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/domains", "request": {"$ref": "Domain"}, "response": {"$ref": "Domain"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Deletes the existing domain mapping on the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/domains/{domainsId}", "httpMethod": "DELETE", "id": "firebasehosting.projects.sites.domains.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the domain association to delete.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/domains/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets a domain mapping on the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/domains/{domainsId}", "httpMethod": "GET", "id": "firebasehosting.projects.sites.domains.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the domain configuration to get.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/domains/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Domain"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the domains for the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/domains", "httpMethod": "GET", "id": "firebasehosting.projects.sites.domains.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The page size to return. Defaults to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token from a previous request, if provided.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent for which to list domains, in the format: sites/ site-name", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/domains", "response": {"$ref": "ListDomainsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "update": {"description": "Updates the specified domain mapping, creating the mapping as if it does not exist.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/domains/{domainsId}", "httpMethod": "PUT", "id": "firebasehosting.projects.sites.domains.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the domain association to update or create, if an association doesn't already exist.", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/domains/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Domain"}, "response": {"$ref": "Domain"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}}, "releases": {"methods": {"create": {"description": "Creates a new release, which makes the content of the specified version actively display on the appropriate URL(s).", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/releases", "httpMethod": "POST", "id": "firebasehosting.projects.sites.releases.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The site or channel to which the release belongs, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}, "versionName": {"description": " The unique identifier for a version, in the format: sites/SITE_ID/versions/ VERSION_ID The SITE_ID in this version identifier must match the SITE_ID in the `parent` parameter. This query parameter must be empty if the `type` field in the request body is `SITE_DISABLE`.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/releases", "request": {"$ref": "Release"}, "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified release for a site or channel. When used to get a release for a site, this can get releases for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/releases/{releasesId}", "httpMethod": "GET", "id": "firebasehosting.projects.sites.releases.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the Hosting release, in either of the following formats: - sites/SITE_ID/channels/CHANNEL_ID/releases/RELEASE_ID - sites/SITE_ID/releases/RELEASE_ID ", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/releases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the releases that have been created for the specified site or channel. When used to list releases for a site, this list includes releases for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/releases", "httpMethod": "GET", "id": "firebasehosting.projects.sites.releases.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of releases to return. The service may return a lower number if fewer releases exist than this maximum number. If unspecified, defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `releases.list` or `channels.releases.list` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site or channel for which to list releases, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID ", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/releases", "response": {"$ref": "ListReleasesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}, "versions": {"methods": {"clone": {"description": "Creates a new version on the specified target site using the content of the specified version.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/versions:clone", "httpMethod": "POST", "id": "firebasehosting.projects.sites.versions.clone", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The target site for the cloned version, in the format: sites/ SITE_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/versions:clone", "request": {"$ref": "CloneVersionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "create": {"description": "Creates a new version for the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/versions", "httpMethod": "POST", "id": "firebasehosting.projects.sites.versions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The site in which to create the version, in the format: sites/ SITE_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}, "sizeBytes": {"description": "The self-reported size of the version. This value is used for a pre-emptive quota check for legacy version uploads.", "format": "int64", "location": "query", "type": "string"}, "versionId": {"deprecated": true, "description": "A unique id for the new version. This is was only specified for legacy version creations, and should be blank.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/versions", "request": {"$ref": "Version"}, "response": {"$ref": "Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Deletes the specified version.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/versions/{versionsId}", "httpMethod": "DELETE", "id": "firebasehosting.projects.sites.versions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the version, in the format: sites/SITE_ID/versions/VERSION_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Get the specified version that has been created for the specified site. This can include versions that were created for the default `live` channel or for any active preview channels for the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/versions/{versionsId}", "httpMethod": "GET", "id": "firebasehosting.projects.sites.versions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the version, in the format: sites/SITE_ID/versions/VERSION_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the versions that have been created for the specified site. This list includes versions for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/versions", "httpMethod": "GET", "id": "firebasehosting.projects.sites.versions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter string used to return a subset of versions in the response. The currently supported fields for filtering are: `name`, `status`, and `create_time`. Learn more about filtering in Google's [AIP 160 standard](https://google.aip.dev/160).", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of versions to return. The service may return a lower number if fewer versions exist than this maximum number. If unspecified, defaults to 25. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `ListVersions` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site or channel for which to list versions, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID ", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/versions", "response": {"$ref": "ListVersionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": " Updates the specified metadata for the specified version. This method will fail with `FAILED_PRECONDITION` in the event of an invalid state transition. The supported [state](../sites.versions#versionstatus) transitions for a version are from `CREATED` to `FINALIZED`. Use [`DeleteVersion`](delete) to set the status of a version to `DELETED`.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/versions/{versionsId}", "httpMethod": "PATCH", "id": "firebasehosting.projects.sites.versions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The fully-qualified resource name for the version, in the format: sites/ SITE_ID/versions/VERSION_ID This name is provided in the response body when you call [`CreateVersion`](sites.versions/create).", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "A set of field names from your [version](../sites.versions) that you want to update. A field will be overwritten if, and only if, it's in the mask. If a mask is not provided then a default mask of only [`status`](../sites.versions#Version.FIELDS.status) will be used.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Version"}, "response": {"$ref": "Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "populateFiles": {"description": " Adds content files to the specified version. Each file must be under 2 GB.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/versions/{versionsId}:populateFiles", "httpMethod": "POST", "id": "firebasehosting.projects.sites.versions.populateFiles", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The version to which to add files, in the format: sites/SITE_ID /versions/VERSION_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}:populateFiles", "request": {"$ref": "PopulateVersionFilesRequest"}, "response": {"$ref": "PopulateVersionFilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}, "resources": {"files": {"methods": {"list": {"description": "Lists the remaining files to be uploaded for the specified version.", "flatPath": "v1beta1/projects/{projectsId}/sites/{sitesId}/versions/{versionsId}/files", "httpMethod": "GET", "id": "firebasehosting.projects.sites.versions.files.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of version files to return. The service may return a lower number if fewer version files exist than this maximum number. If unspecified, defaults to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `ListVersionFiles` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The version for which to list files, in the format: sites/SITE_ID /versions/VERSION_ID", "location": "path", "pattern": "^projects/[^/]+/sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}, "status": {"description": " The type of files that should be listed for the specified version.", "enum": ["STATUS_UNSPECIFIED", "EXPECTED", "ACTIVE"], "enumDescriptions": ["The default status; should not be intentionally used.", "The file has been included in the version and is expected to be uploaded in the near future.", "The file has already been uploaded to Firebase Hosting."], "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/files", "response": {"$ref": "ListVersionFilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}}}}}}}, "sites": {"methods": {"getConfig": {"description": "Gets the Hosting metadata for a specific site.", "flatPath": "v1beta1/sites/{sitesId}/config", "httpMethod": "GET", "id": "firebasehosting.sites.getConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The site for which to get the SiteConfig, in the format: sites/ site-name/config", "location": "path", "pattern": "^sites/[^/]+/config$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "SiteConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "updateConfig": {"description": "Sets the Hosting metadata for a specific site.", "flatPath": "v1beta1/sites/{sitesId}/config", "httpMethod": "PATCH", "id": "firebasehosting.sites.updateConfig", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The site for which to update the SiteConfig, in the format: sites/ site-name/config", "location": "path", "pattern": "^sites/[^/]+/config$", "required": true, "type": "string"}, "updateMask": {"description": "A set of field names from your [site configuration](../sites.SiteConfig) that you want to update. A field will be overwritten if, and only if, it's in the mask. If a mask is not provided then a default mask of only [`max_versions`](../sites.SiteConfig.max_versions) will be used.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "SiteConfig"}, "response": {"$ref": "SiteConfig"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}, "resources": {"channels": {"methods": {"create": {"description": "Creates a new channel in the specified site.", "flatPath": "v1beta1/sites/{sitesId}/channels", "httpMethod": "POST", "id": "firebasehosting.sites.channels.create", "parameterOrder": ["parent"], "parameters": {"channelId": {"description": "Required. Immutable. A unique ID within the site that identifies the channel.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site in which to create this channel, in the format: sites/ SITE_ID", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/channels", "request": {"$ref": "Channel"}, "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Deletes the specified channel of the specified site. The `live` channel cannot be deleted.", "flatPath": "v1beta1/sites/{sitesId}/channels/{channelsId}", "httpMethod": "DELETE", "id": "firebasehosting.sites.channels.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the channel, in the format: sites/SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Retrieves information for the specified channel of the specified site.", "flatPath": "v1beta1/sites/{sitesId}/channels/{channelsId}", "httpMethod": "GET", "id": "firebasehosting.sites.channels.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the channel, in the format: sites/SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the channels for the specified site. All sites have a default `live` channel.", "flatPath": "v1beta1/sites/{sitesId}/channels", "httpMethod": "GET", "id": "firebasehosting.sites.channels.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of channels to return. The service may return a lower number if fewer channels exist than this maximum number. If unspecified, defaults to 10. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `ListChannels` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site for which to list channels, in the format: sites/SITE_ID", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/channels", "response": {"$ref": "ListChannelsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": "Updates information for the specified channel of the specified site. Implicitly creates the channel if it doesn't already exist.", "flatPath": "v1beta1/sites/{sitesId}/channels/{channelsId}", "httpMethod": "PATCH", "id": "firebasehosting.sites.channels.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The fully-qualified resource name for the channel, in the format: sites/ SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "A comma-separated list of fields to be updated in this request.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Channel"}, "response": {"$ref": "Channel"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}, "resources": {"releases": {"methods": {"create": {"description": "Creates a new release, which makes the content of the specified version actively display on the appropriate URL(s).", "flatPath": "v1beta1/sites/{sitesId}/channels/{channelsId}/releases", "httpMethod": "POST", "id": "firebasehosting.sites.channels.releases.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The site or channel to which the release belongs, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}, "versionName": {"description": " The unique identifier for a version, in the format: sites/SITE_ID/versions/ VERSION_ID The SITE_ID in this version identifier must match the SITE_ID in the `parent` parameter. This query parameter must be empty if the `type` field in the request body is `SITE_DISABLE`.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/releases", "request": {"$ref": "Release"}, "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified release for a site or channel. When used to get a release for a site, this can get releases for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/sites/{sitesId}/channels/{channelsId}/releases/{releasesId}", "httpMethod": "GET", "id": "firebasehosting.sites.channels.releases.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the Hosting release, in either of the following formats: - sites/SITE_ID/channels/CHANNEL_ID/releases/RELEASE_ID - sites/SITE_ID/releases/RELEASE_ID ", "location": "path", "pattern": "^sites/[^/]+/channels/[^/]+/releases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the releases that have been created for the specified site or channel. When used to list releases for a site, this list includes releases for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/sites/{sitesId}/channels/{channelsId}/releases", "httpMethod": "GET", "id": "firebasehosting.sites.channels.releases.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of releases to return. The service may return a lower number if fewer releases exist than this maximum number. If unspecified, defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `releases.list` or `channels.releases.list` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site or channel for which to list releases, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID ", "location": "path", "pattern": "^sites/[^/]+/channels/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/releases", "response": {"$ref": "ListReleasesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}}}, "domains": {"methods": {"create": {"description": "Creates a domain mapping on the specified site.", "flatPath": "v1beta1/sites/{sitesId}/domains", "httpMethod": "POST", "id": "firebasehosting.sites.domains.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent to create the domain association for, in the format: sites/site-name", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/domains", "request": {"$ref": "Domain"}, "response": {"$ref": "Domain"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Deletes the existing domain mapping on the specified site.", "flatPath": "v1beta1/sites/{sitesId}/domains/{domainsId}", "httpMethod": "DELETE", "id": "firebasehosting.sites.domains.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the domain association to delete.", "location": "path", "pattern": "^sites/[^/]+/domains/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets a domain mapping on the specified site.", "flatPath": "v1beta1/sites/{sitesId}/domains/{domainsId}", "httpMethod": "GET", "id": "firebasehosting.sites.domains.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the domain configuration to get.", "location": "path", "pattern": "^sites/[^/]+/domains/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Domain"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the domains for the specified site.", "flatPath": "v1beta1/sites/{sitesId}/domains", "httpMethod": "GET", "id": "firebasehosting.sites.domains.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The page size to return. Defaults to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The next_page_token from a previous request, if provided.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent for which to list domains, in the format: sites/ site-name", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/domains", "response": {"$ref": "ListDomainsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "update": {"description": "Updates the specified domain mapping, creating the mapping as if it does not exist.", "flatPath": "v1beta1/sites/{sitesId}/domains/{domainsId}", "httpMethod": "PUT", "id": "firebasehosting.sites.domains.update", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the domain association to update or create, if an association doesn't already exist.", "location": "path", "pattern": "^sites/[^/]+/domains/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Domain"}, "response": {"$ref": "Domain"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}}, "releases": {"methods": {"create": {"description": "Creates a new release, which makes the content of the specified version actively display on the appropriate URL(s).", "flatPath": "v1beta1/sites/{sitesId}/releases", "httpMethod": "POST", "id": "firebasehosting.sites.releases.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The site or channel to which the release belongs, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}, "versionName": {"description": " The unique identifier for a version, in the format: sites/SITE_ID/versions/ VERSION_ID The SITE_ID in this version identifier must match the SITE_ID in the `parent` parameter. This query parameter must be empty if the `type` field in the request body is `SITE_DISABLE`.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/releases", "request": {"$ref": "Release"}, "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Gets the specified release for a site or channel. When used to get a release for a site, this can get releases for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/sites/{sitesId}/releases/{releasesId}", "httpMethod": "GET", "id": "firebasehosting.sites.releases.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the Hosting release, in either of the following formats: - sites/SITE_ID/channels/CHANNEL_ID/releases/RELEASE_ID - sites/SITE_ID/releases/RELEASE_ID ", "location": "path", "pattern": "^sites/[^/]+/releases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Release"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the releases that have been created for the specified site or channel. When used to list releases for a site, this list includes releases for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/sites/{sitesId}/releases", "httpMethod": "GET", "id": "firebasehosting.sites.releases.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of releases to return. The service may return a lower number if fewer releases exist than this maximum number. If unspecified, defaults to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `releases.list` or `channels.releases.list` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site or channel for which to list releases, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID ", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/releases", "response": {"$ref": "ListReleasesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}, "versions": {"methods": {"clone": {"description": "Creates a new version on the specified target site using the content of the specified version.", "flatPath": "v1beta1/sites/{sitesId}/versions:clone", "httpMethod": "POST", "id": "firebasehosting.sites.versions.clone", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The target site for the cloned version, in the format: sites/ SITE_ID", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/versions:clone", "request": {"$ref": "CloneVersionRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "create": {"description": "Creates a new version for the specified site.", "flatPath": "v1beta1/sites/{sitesId}/versions", "httpMethod": "POST", "id": "firebasehosting.sites.versions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The site in which to create the version, in the format: sites/ SITE_ID", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}, "sizeBytes": {"description": "The self-reported size of the version. This value is used for a pre-emptive quota check for legacy version uploads.", "format": "int64", "location": "query", "type": "string"}, "versionId": {"deprecated": true, "description": "A unique id for the new version. This is was only specified for legacy version creations, and should be blank.", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/versions", "request": {"$ref": "Version"}, "response": {"$ref": "Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "delete": {"description": "Deletes the specified version.", "flatPath": "v1beta1/sites/{sitesId}/versions/{versionsId}", "httpMethod": "DELETE", "id": "firebasehosting.sites.versions.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the version, in the format: sites/SITE_ID/versions/VERSION_ID", "location": "path", "pattern": "^sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "get": {"description": "Get the specified version that has been created for the specified site. This can include versions that were created for the default `live` channel or for any active preview channels for the specified site.", "flatPath": "v1beta1/sites/{sitesId}/versions/{versionsId}", "httpMethod": "GET", "id": "firebasehosting.sites.versions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The fully-qualified resource name for the version, in the format: sites/SITE_ID/versions/VERSION_ID", "location": "path", "pattern": "^sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "list": {"description": "Lists the versions that have been created for the specified site. This list includes versions for both the default `live` channel and any active preview channels for the specified site.", "flatPath": "v1beta1/sites/{sitesId}/versions", "httpMethod": "GET", "id": "firebasehosting.sites.versions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A filter string used to return a subset of versions in the response. The currently supported fields for filtering are: `name`, `status`, and `create_time`. Learn more about filtering in Google's [AIP 160 standard](https://google.aip.dev/160).", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of versions to return. The service may return a lower number if fewer versions exist than this maximum number. If unspecified, defaults to 25. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `ListVersions` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The site or channel for which to list versions, in either of the following formats: - sites/SITE_ID - sites/SITE_ID/channels/CHANNEL_ID ", "location": "path", "pattern": "^sites/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/versions", "response": {"$ref": "ListVersionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}, "patch": {"description": " Updates the specified metadata for the specified version. This method will fail with `FAILED_PRECONDITION` in the event of an invalid state transition. The supported [state](../sites.versions#versionstatus) transitions for a version are from `CREATED` to `FINALIZED`. Use [`DeleteVersion`](delete) to set the status of a version to `DELETED`.", "flatPath": "v1beta1/sites/{sitesId}/versions/{versionsId}", "httpMethod": "PATCH", "id": "firebasehosting.sites.versions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The fully-qualified resource name for the version, in the format: sites/ SITE_ID/versions/VERSION_ID This name is provided in the response body when you call [`CreateVersion`](sites.versions/create).", "location": "path", "pattern": "^sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "A set of field names from your [version](../sites.versions) that you want to update. A field will be overwritten if, and only if, it's in the mask. If a mask is not provided then a default mask of only [`status`](../sites.versions#Version.FIELDS.status) will be used.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Version"}, "response": {"$ref": "Version"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}, "populateFiles": {"description": " Adds content files to the specified version. Each file must be under 2 GB.", "flatPath": "v1beta1/sites/{sitesId}/versions/{versionsId}:populateFiles", "httpMethod": "POST", "id": "firebasehosting.sites.versions.populateFiles", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The version to which to add files, in the format: sites/SITE_ID /versions/VERSION_ID", "location": "path", "pattern": "^sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}:populateFiles", "request": {"$ref": "PopulateVersionFilesRequest"}, "response": {"$ref": "PopulateVersionFilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/firebase"]}}, "resources": {"files": {"methods": {"list": {"description": "Lists the remaining files to be uploaded for the specified version.", "flatPath": "v1beta1/sites/{sitesId}/versions/{versionsId}/files", "httpMethod": "GET", "id": "firebasehosting.sites.versions.files.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "The maximum number of version files to return. The service may return a lower number if fewer version files exist than this maximum number. If unspecified, defaults to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token from a previous call to `ListVersionFiles` that tells the server where to resume listing.", "location": "query", "type": "string"}, "parent": {"description": "Required. The version for which to list files, in the format: sites/SITE_ID /versions/VERSION_ID", "location": "path", "pattern": "^sites/[^/]+/versions/[^/]+$", "required": true, "type": "string"}, "status": {"description": " The type of files that should be listed for the specified version.", "enum": ["STATUS_UNSPECIFIED", "EXPECTED", "ACTIVE"], "enumDescriptions": ["The default status; should not be intentionally used.", "The file has been included in the version and is expected to be uploaded in the near future.", "The file has already been uploaded to Firebase Hosting."], "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/files", "response": {"$ref": "ListVersionFilesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only", "https://www.googleapis.com/auth/firebase", "https://www.googleapis.com/auth/firebase.readonly"]}}}}}}}}, "revision": "********", "rootUrl": "https://firebasehosting.googleapis.com/", "schemas": {"ActingUser": {"description": "Contains metadata about the user who performed an action, such as creating a release or finalizing a version.", "id": "ActingUser", "properties": {"email": {"description": "The email address of the user when the user performed the action.", "type": "string"}, "imageUrl": {"description": "A profile image URL for the user. May not be present if the user has changed their email address or deleted their account.", "type": "string"}}, "type": "object"}, "CertDnsChallenge": {"description": "Represents a DNS certificate challenge.", "id": "CertDnsChallenge", "properties": {"domainName": {"description": "The domain name upon which the DNS challenge must be satisfied.", "type": "string"}, "token": {"description": "The value that must be present as a TXT record on the domain name to satisfy the challenge.", "type": "string"}}, "type": "object"}, "CertHttpChallenge": {"description": "Represents an HTTP certificate challenge.", "id": "CertHttpChallenge", "properties": {"path": {"description": "The URL path on which to serve the specified token to satisfy the certificate challenge.", "type": "string"}, "token": {"description": "The token to serve at the specified URL path to satisfy the certificate challenge.", "type": "string"}}, "type": "object"}, "CertVerification": {"description": "A set of ACME challenges you can use to allow Hosting to create an SSL certificate for your domain name before directing traffic to Hosting servers. Use either the DNS or HTTP challenge; it's not necessary to provide both.", "id": "CertVerification", "properties": {"dns": {"$ref": "DnsUpdates", "description": "Output only. A `TXT` record to add to your DNS records that confirms your intent to let Hosting create an SSL cert for your domain name.", "readOnly": true}, "http": {"$ref": "HttpUpdate", "description": "Output only. A file to add to your existing, non-Hosting hosting service that confirms your intent to let Hosting create an SSL cert for your domain name.", "readOnly": true}}, "type": "object"}, "Certificate": {"description": "An SSL certificate used to provide end-to-end encryption for requests against your domain name. A `Certificate` can be an actual SSL certificate or, for newly-created custom domains, Hosting's intent to create one.", "id": "Certificate", "properties": {"createTime": {"description": "Output only. The certificate's creation time. For `TEMPORARY` certs this is the time Hosting first generated challenges for your domain name. For all other cert types, it's the time the actual cert was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "expireTime": {"description": "Output only. The certificate's expiration time. After this time, the cert can no longer be used to provide secure communication between Hosting and your site's visitors.", "format": "google-datetime", "readOnly": true, "type": "string"}, "issues": {"description": "Output only. A set of errors Hosting encountered when attempting to create a cert for your domain name. Resolve these issues to ensure Hosting is able to provide secure communication with your site's visitors.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. The state of the certificate. Only the `CERT_ACTIVE` and `CERT_EXPIRING_SOON` states provide SSL coverage for a domain name. If the state is `PROPAGATING` and Hosting had an active cert for the domain name before, that formerly-active cert provides SSL coverage for the domain name until the current cert propagates.", "enum": ["CERT_STATE_UNSPECIFIED", "CERT_PREPARING", "CERT_VALIDATING", "CERT_PROPAGATING", "CERT_ACTIVE", "CERT_EXPIRING_SOON", "CERT_EXPIRED"], "enumDescriptions": ["The certificate's state is unspecified. The message is invalid if this is unspecified.", "The initial state of every certificate, represents Hosting's intent to create a certificate, before requests to a Certificate Authority are made.", "Hosting is validating whether a domain name's DNS records are in a state that allow certificate creation on its behalf.", "The certificate was recently created, and needs time to propagate in Hosting's CDN.", "The certificate is active, providing secure connections for the domain names it represents.", "The certificate is expiring, all domain names on it will be given new certificates.", "The certificate has expired. Hosting can no longer serve secure content on your domain name."], "readOnly": true, "type": "string"}, "type": {"description": "Output only. The certificate's type.", "enum": ["TYPE_UNSPECIFIED", "TEMPORARY", "GROUPED", "PROJECT_GROUPED", "DEDICATED"], "enumDescriptions": ["The certificate's type is unspecified. The message is invalid if this is unspecified.", "A short-lived certificate type that covers a domain name temporarily, while Hosting creates a more permanent certificate.", "The standard certificate for Spark plan custom domains.", "Blaze plan only. A certificate that covers from 1 to 100 domain names with custom domains on the same Firebase project.", "Blaze plan only. A certificate that covers a single domain name."], "readOnly": true, "type": "string"}, "verification": {"$ref": "CertVerification", "description": "Output only. A set of ACME challenges you can add to your DNS records or existing, non-Hosting hosting provider to allow Hosting to create an SSL certificate for your domain name before you point traffic toward hosting. You can use thse challenges as part of a zero downtime transition from your old provider to Hosting.", "readOnly": true}}, "type": "object"}, "Channel": {"description": "A `Channel` represents a stream of releases for a site. All sites have a default `live` channel that serves content to the Firebase-provided subdomains and any connected custom domains.", "id": "Channel", "properties": {"createTime": {"description": "Output only. The time at which the channel was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "expireTime": {"description": "The time at which the channel will be automatically deleted. If null, the channel will not be automatically deleted. This field is present in the output whether it's set directly or via the `ttl` field.", "format": "google-datetime", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Text labels used for extra metadata and/or filtering.", "type": "object"}, "name": {"description": "The fully-qualified resource name for the channel, in the format: sites/ SITE_ID/channels/CHANNEL_ID", "type": "string"}, "release": {"$ref": "Release", "description": "Output only. The current release for the channel, if any.", "readOnly": true}, "retainedReleaseCount": {"description": "The number of previous releases to retain on the channel for rollback or other purposes. Must be a number between 1-100. Defaults to 10 for new channels.", "format": "int32", "type": "integer"}, "ttl": {"description": "Input only. A time-to-live for this channel. Sets `expire_time` to the provided duration past the time of the request.", "format": "google-duration", "type": "string"}, "updateTime": {"description": "Output only. The time at which the channel was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "url": {"description": "Output only. The URL at which the content of this channel's current release can be viewed. This URL is a Firebase-provided subdomain of `web.app`. The content of this channel's current release can also be viewed at the Firebase-provided subdomain of `firebaseapp.com`. If this channel is the `live` channel for the Hosting site, then the content of this channel's current release can also be viewed at any connected custom domains.", "readOnly": true, "type": "string"}}, "type": "object"}, "CloneVersionRequest": {"id": "CloneVersionRequest", "properties": {"exclude": {"$ref": "Path<PERSON><PERSON>er", "description": "If provided, only paths that do not match any of the RegEx values in this list will be included in the new version."}, "finalize": {"description": "If true, the call to `<PERSON>loneVersion` immediately finalizes the version after cloning is complete. If false, the cloned version will have a status of `CREATED`. Use [`UpdateVersion`](patch) to set the status of the version to `FINALIZED`.", "type": "boolean"}, "include": {"$ref": "Path<PERSON><PERSON>er", "description": "If provided, only paths that match one or more RegEx values in this list will be included in the new version."}, "sourceVersion": {"description": "Required. The unique identifier for the version to be cloned, in the format: sites/SITE_ID/versions/VERSION_ID", "type": "string"}}, "type": "object"}, "CloudRunRewrite": {"description": "A configured rewrite that directs requests to a Cloud Run service. If the Cloud Run service does not exist when setting or updating your Firebase Hosting configuration, then the request fails. Any errors from the Cloud Run service are passed to the end user (for example, if you delete a service, any requests directed to that service receive a `404` error).", "id": "CloudRunRewrite", "properties": {"region": {"description": "Optional. User-provided region where the Cloud Run service is hosted. Defaults to `us-central1` if not supplied.", "type": "string"}, "serviceId": {"description": "Required. User-defined ID of the Cloud Run service.", "type": "string"}, "tag": {"description": "Optional. User-provided TrafficConfig tag to send traffic to. When omitted, traffic is sent to the service-wide URI", "type": "string"}}, "type": "object"}, "CustomDomain": {"description": "A `CustomDomain` is an entity that links a domain name to a Firebase Hosting site. Add a `CustomDomain` to your site to allow Hosting to serve the site's content in response to requests against your domain name.", "id": "CustomDomain", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Annotations you can add to leave both human- and machine-readable metadata about your `CustomDomain`.", "type": "object"}, "cert": {"$ref": "Certificate", "description": "Output only. The SSL certificate Hosting has for this custom domain's domain name. For new custom domains, this often represents Hosting's intent to create a certificate, rather than an actual cert. Check the `state` field for more.", "readOnly": true}, "certPreference": {"description": "A field that lets you specify which SSL certificate type Hosting creates for your domain name. Spark plan custom domains only have access to the `GROUPED` cert type, while Blaze plan domains can select any option.", "enum": ["TYPE_UNSPECIFIED", "TEMPORARY", "GROUPED", "PROJECT_GROUPED", "DEDICATED"], "enumDescriptions": ["The certificate's type is unspecified. The message is invalid if this is unspecified.", "A short-lived certificate type that covers a domain name temporarily, while Hosting creates a more permanent certificate.", "The standard certificate for Spark plan custom domains.", "Blaze plan only. A certificate that covers from 1 to 100 domain names with custom domains on the same Firebase project.", "Blaze plan only. A certificate that covers a single domain name."], "type": "string"}, "createTime": {"description": "Output only. The custom domain's create time.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. The time the `CustomDomain` was deleted; null for custom domains that haven't been deleted. Deleted custom domains persist for approximately 30 days, after which time Hosting removes them completely. To restore a deleted custom domain, make an `UndeleteCustomDomain` request.", "format": "google-datetime", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. A string that represents the current state of the `CustomDomain` and allows you to confirm its initial state in requests that would modify it. Use the tag to ensure consistency when making `UpdateCustomDomain`, `DeleteCustomDomain`, and `UndeleteCustomDomain` requests.", "readOnly": true, "type": "string"}, "expireTime": {"description": "Output only. The minimum time before a soft-deleted `CustomDomain` is completely removed from Hosting; null for custom domains that haven't been deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "hostState": {"description": "Output only. The `HostState` of the domain name this `CustomDomain` refers to.", "enum": ["HOST_STATE_UNSPECIFIED", "HOST_UNHOSTED", "HOST_UNREACHABLE", "HOST_MISMATCH", "HOST_CONFLICT", "HOST_ACTIVE"], "enumDescriptions": ["Your custom domain's host state is unspecified. The message is invalid if this is unspecified.", "Your custom domain's domain name isn't associated with any IP addresses.", "Your custom domain's domain name can't be reached. Hosting services' DNS queries to find your domain name's IP addresses resulted in errors. See your `CustomDomain` object's `issues` field for more details.", "Your custom domain's domain name has IP addresses that don't ultimately resolve to Hosting.", "Your custom domain's domain name has IP addresses that resolve to both Hosting and other services. To ensure consistent results, remove `A` and `AAAA` records related to non-Hosting services.", "All requests against your custom domain's domain name are served by Hosting. If the custom domain's `OwnershipState` is also `ACTIVE`, Hosting serves your Hosting site's content on the domain name."], "readOnly": true, "type": "string"}, "issues": {"description": "Output only. A set of errors Hosting systems encountered when trying to establish Hosting's ability to serve secure content for your domain name. Resolve these issues to ensure your `CustomDomain` behaves properly.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels used for extra metadata and/or filtering.", "type": "object"}, "name": {"description": "Output only. The fully-qualified name of the `CustomDomain`.", "readOnly": true, "type": "string"}, "ownershipState": {"description": "Output only. The `OwnershipState` of the domain name this `CustomDomain` refers to.", "enum": ["OWNERSHIP_STATE_UNSPECIFIED", "OWNERSHIP_MISSING", "OWNERSHIP_UNREACHABLE", "OWNERSHIP_MISMATCH", "OWNERSHIP_CONFLICT", "OWNERSHIP_PENDING", "OWNERSHIP_ACTIVE"], "enumDescriptions": ["Your custom domain's ownership state is unspecified. This should never happen.", "Your custom domain's domain name has no Hosting-related ownership records; no Firebase project has permission to act on the domain name's behalf.", "Your custom domain's domain name can't be reached. Hosting services' DNS queries to find your domain name's ownership records resulted in errors. See your `CustomDomain` object's `issues` field for more details.", "Your custom domain's domain name is owned by another Firebase project. Remove the conflicting `TXT` records and replace them with project-specific records for your current Firebase project.", "Your custom domain's domain name has conflicting `TXT` records that indicate ownership by both your current Firebase project and another project. Remove the other project's ownership records to grant the current project ownership.", "Your custom domain's DNS records are configured correctly. Hosting will transfer ownership of your domain to this `CustomDomain` within 24 hours.", "Your custom domain's domain name has `TXT` records that grant its project permission to act on its behalf."], "readOnly": true, "type": "string"}, "reconciling": {"description": "Output only. A field that, if true, indicates that Hosting's systems are attmepting to make the custom domain's state match your preferred state. This is most frequently `true` when initially provisioning a `CustomDomain` after a `CreateCustomDomain` request or when creating a new SSL certificate to match an updated `cert_preference` after an `UpdateCustomDomain` request.", "readOnly": true, "type": "boolean"}, "redirectTarget": {"description": "A domain name that this `CustomDomain` should direct traffic towards. If specified, Hosting will respond to requests against this custom domain with an HTTP 301 code, and route traffic to the specified `redirect_target` instead.", "type": "string"}, "requiredDnsUpdates": {"$ref": "DnsUpdates", "description": "Output only. A set of updates you should make to the domain name's DNS records to let Hosting serve secure content on its behalf.", "readOnly": true}, "updateTime": {"description": "Output only. The last time the `CustomDomain` was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "CustomDomainMetadata": {"description": "Metadata associated with a`CustomDomain` operation.", "id": "CustomDomainMetadata", "properties": {"certState": {"description": "The `CertState` of the domain name's SSL certificate.", "enum": ["CERT_STATE_UNSPECIFIED", "CERT_PREPARING", "CERT_VALIDATING", "CERT_PROPAGATING", "CERT_ACTIVE", "CERT_EXPIRING_SOON", "CERT_EXPIRED"], "enumDescriptions": ["The certificate's state is unspecified. The message is invalid if this is unspecified.", "The initial state of every certificate, represents Hosting's intent to create a certificate, before requests to a Certificate Authority are made.", "Hosting is validating whether a domain name's DNS records are in a state that allow certificate creation on its behalf.", "The certificate was recently created, and needs time to propagate in Hosting's CDN.", "The certificate is active, providing secure connections for the domain names it represents.", "The certificate is expiring, all domain names on it will be given new certificates.", "The certificate has expired. Hosting can no longer serve secure content on your domain name."], "type": "string"}, "hostState": {"description": "The `HostState` of the domain name this `CustomDomain` refers to.", "enum": ["HOST_STATE_UNSPECIFIED", "HOST_UNHOSTED", "HOST_UNREACHABLE", "HOST_MISMATCH", "HOST_CONFLICT", "HOST_ACTIVE"], "enumDescriptions": ["Your custom domain's host state is unspecified. The message is invalid if this is unspecified.", "Your custom domain's domain name isn't associated with any IP addresses.", "Your custom domain's domain name can't be reached. Hosting services' DNS queries to find your domain name's IP addresses resulted in errors. See your `CustomDomain` object's `issues` field for more details.", "Your custom domain's domain name has IP addresses that don't ultimately resolve to Hosting.", "Your custom domain's domain name has IP addresses that resolve to both Hosting and other services. To ensure consistent results, remove `A` and `AAAA` records related to non-Hosting services.", "All requests against your custom domain's domain name are served by Hosting. If the custom domain's `OwnershipState` is also `ACTIVE`, Hosting serves your Hosting site's content on the domain name."], "type": "string"}, "issues": {"description": "A list of issues that are currently preventing Hosting from completing the operation. These are generally DNS-related issues that Host<PERSON> encounters when querying a domain name's records or attempting to mint an SSL certificate.", "items": {"$ref": "Status"}, "type": "array"}, "liveMigrationSteps": {"description": "A set of DNS record updates and ACME challenges that allow you to transition domain names to Firebase Hosting with zero downtime. These updates allow Hosting to create an SSL certificate and establish ownership for your custom domain before Hosting begins serving traffic on it. If your domain name is already in active use with another provider, add one of the challenges and make the recommended DNS updates. After adding challenges and adjusting DNS records as necessary, wait for the `ownershipState` to be `OWNERSHIP_ACTIVE` and the `certState` to be `CERT_ACTIVE` before sending traffic to Hosting.", "items": {"$ref": "LiveMigrationStep"}, "type": "array"}, "ownershipState": {"description": "The `OwnershipState` of the domain name this `CustomDomain` refers to.", "enum": ["OWNERSHIP_STATE_UNSPECIFIED", "OWNERSHIP_MISSING", "OWNERSHIP_UNREACHABLE", "OWNERSHIP_MISMATCH", "OWNERSHIP_CONFLICT", "OWNERSHIP_PENDING", "OWNERSHIP_ACTIVE"], "enumDescriptions": ["Your custom domain's ownership state is unspecified. This should never happen.", "Your custom domain's domain name has no Hosting-related ownership records; no Firebase project has permission to act on the domain name's behalf.", "Your custom domain's domain name can't be reached. Hosting services' DNS queries to find your domain name's ownership records resulted in errors. See your `CustomDomain` object's `issues` field for more details.", "Your custom domain's domain name is owned by another Firebase project. Remove the conflicting `TXT` records and replace them with project-specific records for your current Firebase project.", "Your custom domain's domain name has conflicting `TXT` records that indicate ownership by both your current Firebase project and another project. Remove the other project's ownership records to grant the current project ownership.", "Your custom domain's DNS records are configured correctly. Hosting will transfer ownership of your domain to this `CustomDomain` within 24 hours.", "Your custom domain's domain name has `TXT` records that grant its project permission to act on its behalf."], "type": "string"}, "quickSetupUpdates": {"$ref": "DnsUpdates", "description": "A set of DNS record updates that allow Hosting to serve secure content on your domain name. The record type determines the update's purpose: - `A` and `AAAA`: Updates your domain name's IP addresses so that they direct traffic to Hosting servers. - `TXT`: Updates ownership permissions on your domain name, letting Hosting know that your custom domain's project has permission to perform actions for that domain name. - `CAA`: Updates your domain name's list of authorized Certificate Authorities (CAs). Only present if you have existing `CAA` records that prohibit Hosting's CA from minting certs for your domain name. These updates include all DNS changes you'll need to get started with Hosting, but, if made all at once, can result in a brief period of downtime for your domain name--while Hosting creates and uploads an SSL cert, for example. If you'd like to add your domain name to Hosting without downtime, complete the `liveMigrationSteps` first, before making the remaining updates in this field."}}, "type": "object"}, "DnsRecord": {"description": "DNS records are resource records that define how systems and services should behave when handling requests for a domain name. For example, when you add `A` records to your domain name's DNS records, you're informing other systems (such as your users' web browsers) to contact those IPv4 addresses to retrieve resources relevant to your domain name (such as your Hosting site files).", "id": "DnsRecord", "properties": {"domainName": {"description": "Output only. The domain name the record pertains to, e.g. `foo.bar.com.`.", "readOnly": true, "type": "string"}, "rdata": {"description": "Output only. The data of the record. The meaning of the value depends on record type: - A and AAAA: IP addresses for the domain name. - CNAME: Another domain to check for records. - TXT: Arbitrary text strings associated with the domain name. Hosting uses TXT records to determine which Firebase projects have permission to act on the domain name's behalf. - CAA: The record's flags, tag, and value, e.g. `0 issue \"pki.goog\"`.", "readOnly": true, "type": "string"}, "requiredAction": {"description": "Output only. An enum that indicates the a required action for this record.", "enum": ["NONE", "ADD", "REMOVE"], "enumDescriptions": ["No action necessary.", "Add this record to your DNS records.", "Remove this record from your DNS records."], "readOnly": true, "type": "string"}, "type": {"description": "Output only. The record's type, which determines what data the record contains.", "enum": ["TYPE_UNSPECIFIED", "A", "CNAME", "TXT", "AAAA", "CAA"], "enumDescriptions": ["The record's type is unspecified. The message is invalid if this is unspecified.", "An `A` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). A records determine which IPv4 addresses a domain name directs traffic towards.", "A `CNAME` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). `CNAME` or Canonical Name records map a domain name to a different, canonical domain name. If a `CNAME` record is present, it should be the only record on the domain name.", "A `TXT` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). `TXT` records hold arbitrary text data on a domain name. Hosting uses `TXT` records to establish which Firebase Project has permission to act on a domain name.", "An AAAA record, as defined in [RFC 3596](https://tools.ietf.org/html/rfc3596) AAAA records determine which IPv6 addresses a domain name directs traffic towards.", "A CAA record, as defined in [RFC 6844](https://tools.ietf.org/html/rfc6844). CAA, or Certificate Authority Authorization, records determine which Certificate Authorities (SSL certificate minting organizations) are authorized to mint a certificate for the domain name. Firebase Hosting uses `pki.goog` as its primary CA. CAA records cascade. A CAA record on `foo.com` also applies to `bar.foo.com` unless `bar.foo.com` has its own set of CAA records. CAA records are optional. If a domain name and its parents have no CAA records, all CAs are authorized to mint certificates on its behalf. In general, Hosting only asks you to modify CAA records when doing so is required to unblock SSL cert creation."], "readOnly": true, "type": "string"}}, "type": "object"}, "DnsRecordSet": {"description": "A set of DNS records relevant to the setup and maintenance of a custom domain in Firebase Hosting.", "id": "DnsRecordSet", "properties": {"checkError": {"$ref": "Status", "description": "Output only. An error Hosting services encountered when querying your domain name's DNS records. Note: Hosting ignores `NXDOMAIN` errors, as those generally just mean that a domain name hasn't been set up yet.", "readOnly": true}, "domainName": {"description": "Output only. The domain name the record set pertains to.", "readOnly": true, "type": "string"}, "records": {"description": "Output only. Records on the domain.", "items": {"$ref": "DnsRecord"}, "readOnly": true, "type": "array"}}, "type": "object"}, "DnsUpdates": {"description": "A set of DNS record updates that you should make to allow Hosting to serve secure content in response to requests against your domain name. These updates present the current state of your domain name's DNS records when Hosting last queried them, and the desired set of records that Hosting needs to see before your custom domain can be fully active.", "id": "DnsUpdates", "properties": {"checkTime": {"description": "The last time Hosting checked your custom domain's DNS records.", "format": "google-datetime", "type": "string"}, "desired": {"description": "The set of DNS records Hosting needs to serve secure content on the domain.", "items": {"$ref": "DnsRecordSet"}, "type": "array"}, "discovered": {"description": "The set of DNS records Hosting discovered when inspecting a domain.", "items": {"$ref": "DnsRecordSet"}, "type": "array"}}, "type": "object"}, "Domain": {"description": "The intended behavior and status information of a domain.", "id": "Domain", "properties": {"domainName": {"description": "Required. The domain name of the association.", "type": "string"}, "domainRedirect": {"$ref": "DomainRedirect", "description": "If set, the domain should redirect with the provided parameters."}, "provisioning": {"$ref": "DomainProvisioning", "description": "Output only. Information about the provisioning of certificates and the health of the DNS resolution for the domain."}, "site": {"description": "Required. The site name of the association.", "type": "string"}, "status": {"description": "Output only. Additional status of the domain association.", "enum": ["DOMAIN_STATUS_UNSPECIFIED", "DOMAIN_CHANGE_PENDING", "DOMAIN_ACTIVE", "DOMAIN_VERIFICATION_REQUIRED", "DOMAIN_VERIFICATION_LOST"], "enumDeprecated": [false, true, false, false, false], "enumDescriptions": ["Unspecified domain association status.", "An external operation is in progress on the domain association and no further operations can be performed until it is complete. Formerly used for metabase updates. Not currently used", "The domain association is active and no additional action is required.", "The domain was previously verified in the legacy system. User must reverify the domain through the ownership service.", "The domain verification has been lost and the domain is in the grace period before being removed from the Firebase Hosting site."], "type": "string"}, "updateTime": {"description": "Output only. The time at which the domain was last updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "DomainProvisioning": {"description": "The current certificate provisioning status information for a domain.", "id": "DomainProvisioning", "properties": {"certChallengeDiscoveredTxt": {"description": "The TXT records (for the certificate challenge) that were found at the last DNS fetch.", "items": {"type": "string"}, "type": "array"}, "certChallengeDns": {"$ref": "CertDnsChallenge", "description": "The DNS challenge for generating a certificate."}, "certChallengeHttp": {"$ref": "CertHttpChallenge", "description": "The HTTP challenge for generating a certificate."}, "certStatus": {"description": "The certificate provisioning status; updated when Firebase Hosting provisions an SSL certificate for the domain.", "enum": ["CERT_STATUS_UNSPECIFIED", "CERT_PENDING", "CERT_MISSING", "CERT_PROCESSING", "CERT_PROPAGATING", "CERT_ACTIVE", "CERT_ERROR"], "enumDescriptions": ["Unspecified certificate provisioning status.", "Waiting for certificate challenge to be created.", "Waiting for certificate challenge to be met.", "Certificate challenge met; attempting to acquire/propagate certificate.", "Certificate obtained; propagating to the CDN.", "Certificate provisioned and deployed across the CDN.", "Certificate provisioning failed in a non-recoverable manner."], "type": "string"}, "discoveredIps": {"description": "The IPs found at the last DNS fetch.", "items": {"type": "string"}, "type": "array"}, "dnsFetchTime": {"description": "The time at which the last DNS fetch occurred.", "format": "google-datetime", "type": "string"}, "dnsStatus": {"description": "The DNS record match status as of the last DNS fetch.", "enum": ["DNS_STATUS_UNSPECIFIED", "DNS_PENDING", "DNS_MISSING", "DNS_PARTIAL_MATCH", "DNS_MATCH", "DNS_EXTRANEOUS_MATCH"], "enumDescriptions": ["Unspecified DNS status.", "No DNS records have been specified for this domain yet.", "None of the required DNS records have been detected on the domain.", "Some of the required DNS records were detected, but not all of them. No extra (non-required) DNS records were detected.", "All required DNS records were detected. No extra (non-required) DNS records were detected.", "The domain has at least one of the required DNS records, and it has at least one extra (non-required) DNS record."], "type": "string"}, "expectedIps": {"description": "The list of IPs to which the domain is expected to resolve.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "DomainRedirect": {"description": "Defines the behavior of a domain-level redirect. Domain redirects preserve the path of the redirect but replace the requested domain with the one specified in the redirect configuration.", "id": "DomainRedirect", "properties": {"domainName": {"description": "Required. The domain name to redirect to.", "type": "string"}, "type": {"description": "Required. The redirect status code.", "enum": ["REDIRECT_TYPE_UNSPECIFIED", "MOVED_PERMANENTLY"], "enumDescriptions": ["The default redirect type; should not be intentionlly used.", "The redirect will respond with an HTTP status code of `301 Moved Permanently`."], "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Header": {"description": "A [`Header`](https://firebase.google.com/docs/hosting/full-config#headers) specifies a URL pattern that, if matched to the request URL path, triggers Hosting to apply the specified custom response headers.", "id": "Header", "properties": {"glob": {"description": "The user-supplied [glob](https://firebase.google.com/docs/hosting/full-config#glob_pattern_matching) to match against the request URL path.", "type": "string"}, "headers": {"additionalProperties": {"type": "string"}, "description": "Required. The additional headers to add to the response.", "type": "object"}, "regex": {"description": "The user-supplied RE2 regular expression to match against the request URL path.", "type": "string"}}, "type": "object"}, "HttpUpdate": {"description": "A file you can add to your existing, non-Hosting hosting service that confirms your intent to allow Hosting's Certificate Authorities to create an SSL certificate for your domain.", "id": "HttpUpdate", "properties": {"checkError": {"$ref": "Status", "description": "Output only. An error encountered during the last contents check. If null, the check completed successfully.", "readOnly": true}, "desired": {"description": "Output only. A text string to serve at the path.", "readOnly": true, "type": "string"}, "discovered": {"description": "Output only. Whether Host<PERSON> was able to find the required file contents on the specified path during its last check.", "readOnly": true, "type": "string"}, "lastCheckTime": {"description": "Output only. The last time Hosting systems checked for the file contents.", "format": "google-datetime", "readOnly": true, "type": "string"}, "path": {"description": "Output only. The path to the file.", "readOnly": true, "type": "string"}}, "type": "object"}, "I18nConfig": {"description": "If provided, i18n rewrites are enabled.", "id": "I18nConfig", "properties": {"root": {"description": "Required. The user-supplied path where country and language specific content will be looked for within the public directory.", "type": "string"}}, "type": "object"}, "ListChannelsResponse": {"id": "ListChannelsResponse", "properties": {"channels": {"description": "The list of channels.", "items": {"$ref": "Channel"}, "type": "array"}, "nextPageToken": {"description": "The pagination token, if more results exist beyond the ones in this response. Include this token in your next call to `ListChannels`. Page tokens are short-lived and should not be stored.", "type": "string"}}, "type": "object"}, "ListCustomDomainsResponse": {"description": "The response from `ListCustomDomains`.", "id": "ListCustomDomainsResponse", "properties": {"customDomains": {"description": "A list of `CustomDomain` entities associated with the specified Firebase `Site`.", "items": {"$ref": "CustomDomain"}, "type": "array"}, "nextPageToken": {"description": "The pagination token, if more results exist beyond the ones in this response. Include this token in your next call to `ListCustomDomains`. Page tokens are short-lived and should not be stored.", "type": "string"}}, "type": "object"}, "ListDomainsResponse": {"description": "The response to listing Domains.", "id": "ListDomainsResponse", "properties": {"domains": {"description": "The list of domains, if any exist.", "items": {"$ref": "Domain"}, "type": "array"}, "nextPageToken": {"description": "The pagination token, if more results exist.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListReleasesResponse": {"id": "ListReleasesResponse", "properties": {"nextPageToken": {"description": "The pagination token, if more results exist beyond the ones in this response. Include this token in your next call to `ListReleases`. Page tokens are short-lived and should not be stored.", "type": "string"}, "releases": {"description": "The list of hashes of files that still need to be uploaded, if any exist.", "items": {"$ref": "Release"}, "type": "array"}}, "type": "object"}, "ListSitesResponse": {"id": "ListSitesResponse", "properties": {"nextPageToken": {"description": "The pagination token, if more results exist beyond the ones in this response. Include this token in your next call to `ListSites`. Page tokens are short-lived and should not be stored.", "type": "string"}, "sites": {"description": "A list of Site objects associated with the specified Firebase project.", "items": {"$ref": "Site"}, "type": "array"}}, "type": "object"}, "ListVersionFilesResponse": {"id": "ListVersionFilesResponse", "properties": {"files": {"description": " The list of paths to the hashes of the files in the specified version.", "items": {"$ref": "VersionFile"}, "type": "array"}, "nextPageToken": {"description": "The pagination token, if more results exist beyond the ones in this response. Include this token in your next call to `ListVersionFiles`. Page tokens are short-lived and should not be stored.", "type": "string"}}, "type": "object"}, "ListVersionsResponse": {"id": "ListVersionsResponse", "properties": {"nextPageToken": {"description": "The pagination token, if more results exist beyond the ones in this response. Include this token in your next call to `ListVersions`. Page tokens are short-lived and should not be stored.", "type": "string"}, "versions": {"description": "The list of versions, if any exist.", "items": {"$ref": "Version"}, "type": "array"}}, "type": "object"}, "LiveMigrationStep": {"description": "A set of updates including ACME challenges and DNS records that allow Hosting to create an SSL certificate and establish project ownership for your domain name before you direct traffic to Hosting servers. Use these updates to facilitate zero downtime migrations to Hosting from other services. After you've made the recommended updates, check your custom domain's `ownershipState` and `certState`. To avoid downtime, they should be `OWNERSHIP_ACTIVE` and `CERT_ACTIVE`, respectively, before you update your `A` and `AAAA` records.", "id": "LiveMigrationStep", "properties": {"certVerification": {"$ref": "CertVerification", "description": "Output only. A pair of ACME challenges that Hosting's Certificate Authority (CA) can use to create an SSL cert for your domain name. Use either the DNS or HTTP challenge; it's not necessary to provide both.", "readOnly": true}, "dnsUpdates": {"$ref": "DnsUpdates", "description": "Output only. DNS updates to facilitate your domain's zero-downtime migration to Hosting.", "readOnly": true}, "issues": {"description": "Output only. Issues that prevent the current step from completing.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "state": {"description": "Output only. The state of the live migration step, indicates whether you should work to complete the step now, in the future, or have already completed it.", "enum": ["STATE_UNSPECIFIED", "PREPARING", "PENDING", "INCOMPLETE", "PROCESSING", "COMPLETE"], "enumDescriptions": ["The step's state is unspecified. The message is invalid if this is unspecified.", "Hosting doesn't have enough information to construct the step yet. Complete any prior steps and/or resolve this step's issue to proceed.", "The step's state is pending. Complete prior steps before working on a `PENDING` step.", "The step is incomplete. You should complete any `certVerification` or `dnsUpdates` changes to complete it.", "You've done your part to update records and present challenges as necessary. Hosting is now completing background processes to complete the step, e.g. minting an SSL cert for your domain name.", "The step is complete. You've already made the necessary changes to your domain and/or prior hosting service to advance to the next step. Once all steps are complete, Hosting is ready to serve secure content on your domain."], "readOnly": true, "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "PathFilter": {"description": "A representation of filter path.", "id": "Path<PERSON><PERSON>er", "properties": {"regexes": {"description": "An array of RegEx values by which to filter.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PopulateVersionFilesRequest": {"id": "PopulateVersionFilesRequest", "properties": {"files": {"additionalProperties": {"type": "string"}, "description": "A set of file paths to the hashes corresponding to assets that should be added to the version. A file path to an empty hash will remove the path from the version. Calculate a hash by Gzipping the file then taking the SHA256 hash of the newly compressed file.", "type": "object"}}, "type": "object"}, "PopulateVersionFilesResponse": {"id": "PopulateVersionFilesResponse", "properties": {"uploadRequiredHashes": {"description": "The content hashes of the specified files that need to be uploaded to the specified URL.", "items": {"type": "string"}, "type": "array"}, "uploadUrl": {"description": "The URL to which the files should be uploaded, in the format: \"https://upload-firebasehosting.googleapis.com/upload/sites/SITE_ID /versions/VERSION_ID/files\" Perform a multipart `POST` of the Gzipped file contents to the URL using a forward slash and the hash of the file appended to the end.", "type": "string"}}, "type": "object"}, "Redirect": {"description": "A [`Redirect`](https://firebase.google.com/docs/hosting/full-config#redirects) specifies a URL pattern that, if matched to the request URL path, triggers Hosting to respond with a redirect to the specified destination path.", "id": "Redirect", "properties": {"glob": {"description": "The user-supplied [glob](https://firebase.google.com/docs/hosting/full-config#glob_pattern_matching) to match against the request URL path.", "type": "string"}, "location": {"description": "Required. The value to put in the HTTP location header of the response. The location can contain capture group values from the pattern using a `:` prefix to identify the segment and an optional `*` to capture the rest of the URL. For example: \"glob\": \"/:capture*\", \"statusCode\": 301, \"location\": \"https://example.com/foo/:capture\"", "type": "string"}, "regex": {"description": "The user-supplied RE2 regular expression to match against the request URL path.", "type": "string"}, "statusCode": {"description": "Required. The status HTTP code to return in the response. It must be a valid 3xx status code.", "format": "int32", "type": "integer"}}, "type": "object"}, "Release": {"description": " A `Release` is a particular [collection of configurations and files](sites.versions) that is set to be public at a particular time.", "id": "Release", "properties": {"message": {"description": "The deploy description when the release was created. The value can be up to 512 characters.", "type": "string"}, "name": {"description": "Output only. The unique identifier for the release, in either of the following formats: - sites/SITE_ID/releases/RELEASE_ID - sites/SITE_ID/channels/CHANNEL_ID/releases/RELEASE_ID This name is provided in the response body when you call [`releases.create`](sites.releases/create) or [`channels.releases.create`](sites.channels.releases/create).", "type": "string"}, "releaseTime": {"description": "Output only. The time at which the version is set to be public.", "format": "google-datetime", "type": "string"}, "releaseUser": {"$ref": "ActingUser", "description": "Output only. Identifies the user who created the release."}, "type": {"description": "Explains the reason for the release. Specify a value for this field only when creating a `SITE_DISABLE` type release.", "enum": ["TYPE_UNSPECIFIED", "DEPLOY", "ROLLBACK", "SITE_DISABLE"], "enumDescriptions": ["An unspecified type. Indicates that a version was released. This is the default value when no other `type` is explicitly specified.", "A version was uploaded to Firebase Hosting and released.", "The release points back to a previously deployed version.", "The release prevents the site from serving content. Firebase Hosting acts as if the site never existed."], "type": "string"}, "version": {"$ref": "Version", "description": "Output only. The configuration and content that was released."}}, "type": "object"}, "Rewrite": {"description": "A [`Rewrite`](https://firebase.google.com/docs/hosting/full-config#rewrites) specifies a URL pattern that, if matched to the request URL path, triggers Hosting to respond as if the service were given the specified destination URL.", "id": "Rewrite", "properties": {"dynamicLinks": {"description": "The request will be forwarded to Firebase Dynamic Links.", "type": "boolean"}, "function": {"description": "The function to proxy requests to. Must match the exported function name exactly.", "type": "string"}, "functionRegion": {"description": "Optional. Specify a Cloud region for rewritten Functions invocations. If not provided, defaults to us-central1.", "type": "string"}, "glob": {"description": "The user-supplied [glob](https://firebase.google.com/docs/hosting/full-config#glob_pattern_matching) to match against the request URL path.", "type": "string"}, "path": {"description": "The URL path to rewrite the request to.", "type": "string"}, "regex": {"description": "The user-supplied RE2 regular expression to match against the request URL path.", "type": "string"}, "run": {"$ref": "CloudRunRewrite", "description": "The request will be forwarded to Cloud Run."}}, "type": "object"}, "ServingConfig": {"description": "The configuration for how incoming requests to a site should be routed and processed before serving content. The URL request paths are matched against the specified URL patterns in the configuration, then Hosting applies the applicable configuration according to a specific [priority order](https://firebase.google.com/docs/hosting/full-config#hosting_priority_order).", "id": "ServingConfig", "properties": {"appAssociation": {"description": "How to handle well known App Association files.", "enum": ["AUTO", "NONE"], "enumDescriptions": ["The app association files will be automatically created from the apps that exist in the Firebase project.", "No special handling of the app association files will occur, these paths will result in a 404 unless caught with a Rewrite."], "type": "string"}, "cleanUrls": {"description": "Defines whether to drop the file extension from uploaded files.", "type": "boolean"}, "headers": {"description": "An array of objects, where each object specifies a URL pattern that, if matched to the request URL path, triggers Hosting to apply the specified custom response headers.", "items": {"$ref": "Header"}, "type": "array"}, "i18n": {"$ref": "I18nConfig", "description": "Optional. Defines i18n rewrite behavior."}, "redirects": {"description": "An array of objects (called redirect rules), where each rule specifies a URL pattern that, if matched to the request URL path, triggers Hosting to respond with a redirect to the specified destination path.", "items": {"$ref": "Redirect"}, "type": "array"}, "rewrites": {"description": "An array of objects (called rewrite rules), where each rule specifies a URL pattern that, if matched to the request URL path, triggers Hosting to respond as if the service were given the specified destination URL.", "items": {"$ref": "Rewrite"}, "type": "array"}, "trailingSlashBehavior": {"description": "Defines how to handle a trailing slash in the URL path.", "enum": ["TRAILING_SLASH_BEHAVIOR_UNSPECIFIED", "ADD", "REMOVE"], "enumDescriptions": ["No behavior is specified. Files are served at their exact location only, and trailing slashes are only added to directory indexes.", "Trailing slashes are _added_ to directory indexes as well as to any URL path not ending in a file extension.", "Trailing slashes are _removed_ from directory indexes as well as from any URL path not ending in a file extension."], "type": "string"}}, "type": "object"}, "Site": {"description": "A `Site` represents a Firebase Hosting site.", "id": "Site", "properties": {"appId": {"description": "Optional. The [ID of a Web App](https://firebase.google.com/docs/reference/firebase-management/rest/v1beta1/projects.webApps#WebApp.FIELDS.app_id) associated with the Hosting site.", "type": "string"}, "defaultUrl": {"description": "Output only. The default URL for the Hosting site.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. User-specified labels for the Hosting site.", "type": "object"}, "name": {"description": "Output only. The fully-qualified resource name of the Hosting site, in the format: projects/PROJECT_IDENTIFIER/sites/SITE_ID PROJECT_IDENTIFIER: the Firebase project's [`ProjectNumber`](https://firebase.google.com/docs/reference/firebase-management/rest/v1beta1/projects#FirebaseProject.FIELDS.project_number) ***(recommended)*** or its [`ProjectId`](https://firebase.google.com/docs/reference/firebase-management/rest/v1beta1/projects#FirebaseProject.FIELDS.project_id). Learn more about using project identifiers in Google's [AIP 2510 standard](https://google.aip.dev/cloud/2510).", "readOnly": true, "type": "string"}, "type": {"description": "Output only. The type of Hosting site. Every Firebase project has a `DEFAULT_SITE`, which is created when Hosting is provisioned for the project. All additional sites are `USER_SITE`.", "enum": ["TYPE_UNSPECIFIED", "DEFAULT_SITE", "USER_SITE"], "enumDescriptions": ["Unknown state, likely the result of an error on the backend.", "The default Hosting site that is provisioned when a Firebase project is created.", "A Hosting site that the user created."], "readOnly": true, "type": "string"}}, "type": "object"}, "SiteConfig": {"description": "A `SiteConfig` contains metadata associated with a specific site that controls Firebase Hosting serving behavior", "id": "SiteConfig", "properties": {"cloudLoggingEnabled": {"description": "Whether or not web requests made by site visitors are logged via Cloud Logging.", "type": "boolean"}, "maxVersions": {"description": "The number of FINALIZED versions that will be held for a site before automatic deletion. When a new version is deployed, content for versions in storage in excess of this number will be deleted, and will no longer be billed for storage usage. Oldest versions will be deleted first; sites are created with an unlimited number of max_versions by default.", "format": "int64", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "UndeleteCustomDomainRequest": {"description": "The request sent to `UndeleteCustomDomain`.", "id": "UndeleteCustomDomainRequest", "properties": {"etag": {"description": "A tag that represents the state of the `CustomDomain` as you know it. If present, the supplied tag must match the current value on your `CustomDomain`, or the request fails.", "type": "string"}, "validateOnly": {"description": "If true, Hosting validates that it's possible to complete your request but doesn't actually delete the `CustomDomain`.", "type": "boolean"}}, "type": "object"}, "Version": {"description": "A `Version` is a configuration and a collection of static files which determine how a site is displayed.", "id": "Version", "properties": {"config": {"$ref": "ServingConfig", "description": "The configuration for the behavior of the site. This configuration exists in the [`firebase.json`](https://firebase.google.com/docs/cli/#the_firebasejson_file) file."}, "createTime": {"description": "Output only. The time at which the version was created.", "format": "google-datetime", "type": "string"}, "createUser": {"$ref": "ActingUser", "description": "Output only. Identifies the user who created the version."}, "deleteTime": {"description": "Output only. The time at which the version was `DELETED`.", "format": "google-datetime", "type": "string"}, "deleteUser": {"$ref": "ActingUser", "description": "Output only. Identifies the user who `DELETED` the version."}, "fileCount": {"description": "Output only. The total number of files associated with the version. This value is calculated after a version is `FINALIZED`.", "format": "int64", "type": "string"}, "finalizeTime": {"description": "Output only. The time at which the version was `FINALIZED`.", "format": "google-datetime", "type": "string"}, "finalizeUser": {"$ref": "ActingUser", "description": "Output only. Identifies the user who `FINALIZED` the version."}, "labels": {"additionalProperties": {"type": "string"}, "description": "The labels used for extra metadata and/or filtering.", "type": "object"}, "name": {"description": "The fully-qualified resource name for the version, in the format: sites/ SITE_ID/versions/VERSION_ID This name is provided in the response body when you call [`CreateVersion`](sites.versions/create).", "type": "string"}, "status": {"description": "The deploy status of the version. For a successful deploy, call [`CreateVersion`](sites.versions/create) to make a new version (`CREATED` status), [upload all desired files](sites.versions/populateFiles) to the version, then [update](sites.versions/patch) the version to the `FINALIZED` status. Note that if you leave the version in the `CREATED` state for more than 12 hours, the system will automatically mark the version as `ABANDONED`. You can also change the status of a version to `DELETED` by calling [`DeleteVersion`](sites.versions/delete).", "enum": ["VERSION_STATUS_UNSPECIFIED", "CREATED", "FINALIZED", "DELETED", "ABANDONED", "EXPIRED", "CLONING"], "enumDescriptions": ["The default status; should not be intentionally used.", "The version has been created, and content is currently being added to the version.", "All content has been added to the version, and the version can no longer be changed.", "The version has been deleted.", "The version was not updated to `FINALIZED` within 12 hours and was automatically deleted.", "The version is outside the site-configured limit for the number of retained versions, so the version's content is scheduled for deletion.", "The version is being cloned from another version. All content is still being copied over."], "type": "string"}, "versionBytes": {"description": "Output only. The total stored bytesize of the version. This value is calculated after a version is `FINALIZED`.", "format": "int64", "type": "string"}}, "type": "object"}, "VersionFile": {"description": "A static content file that is part of a version.", "id": "VersionFile", "properties": {"hash": {"description": "The SHA256 content hash of the file.", "type": "string"}, "path": {"description": "The URI at which the file's content should display.", "type": "string"}, "status": {"description": "Output only. The current status of a particular file in the specified version. The value will be either `pending upload` or `uploaded`.", "enum": ["STATUS_UNSPECIFIED", "EXPECTED", "ACTIVE"], "enumDescriptions": ["The default status; should not be intentionally used.", "The file has been included in the version and is expected to be uploaded in the near future.", "The file has already been uploaded to Firebase Hosting."], "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Firebase Hosting API", "version": "v1beta1", "version_module": true}