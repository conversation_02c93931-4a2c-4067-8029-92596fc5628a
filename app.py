import os
from flask import Flask, render_template, request, redirect, url_for, flash
from dotenv import load_dotenv
from models import db, User, bcrypt, SearchHistory 
from forms import RegistrationForm, LoginForm
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
import logging
from datetime import datetime
from app_backend.sentiment_logic import get_product_sentiment_analysis

# --- App Initialization & Config ---
load_dotenv()
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'default_fallback_secret_key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URI')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# --- Initialize Extensions ---
db.init_app(app)
bcrypt.init_app(app)

# --- Flask-Login Setup ---
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login' 
login_manager.login_message_category = 'info' 

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# --- Configure Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Create Database Tables ---
with app.app_context():
    db.create_all()

# --- Main Application Routes ---
@app.route('/')
@login_required 
def index():
    search_history = db.session.query(SearchHistory.product_name)\
        .filter_by(user_id=current_user.id)\
        .distinct()\
        .order_by(db.func.max(SearchHistory.search_time).desc())\
        .group_by(SearchHistory.product_name)\
        .limit(10).all()
    history_items = [item[0] for item in search_history]
    
    return render_template('sentiment_analysis.html', username=current_user.username, history=history_items)

@app.route('/results')
@login_required
def results():
    from app_backend.sentiment_logic import get_product_sentiment_analysis
    
    product1_name = request.args.get('product1', None)
    product2_name = request.args.get('product2', None)
    
    if not product1_name or not product1_name.strip():
        flash('Please enter at least one product name to analyze.', 'warning')
        return redirect(url_for('index'))
        
    product1_name = product1_name.strip()
    
    # Save first product to history
    last_search = SearchHistory.query.filter_by(user_id=current_user.id).order_by(SearchHistory.search_time.desc()).first()
    if not last_search or last_search.product_name.lower() != product1_name.lower():
        db.session.add(SearchHistory(product_name=product1_name, user_id=current_user.id))
        db.session.commit()
    else:
        last_search.search_time = datetime.utcnow()
        db.session.commit()
    
    logger.info(f"Rendering results page for product 1: '{product1_name}'")
    results1 = get_product_sentiment_analysis(product1_name)
    logger.info(f"Results1 data: {results1}")
    
    results2 = None
    if product2_name and product2_name.strip():
        product2_name = product2_name.strip()
        last_search_p2 = SearchHistory.query.filter_by(user_id=current_user.id).order_by(SearchHistory.search_time.desc()).first()
        if not last_search_p2 or last_search_p2.product_name.lower() != product2_name.lower():
             db.session.add(SearchHistory(product_name=product2_name, user_id=current_user.id))
             db.session.commit()
        else:
            # Find the history item for product2 and update its timestamp to move it to the top
            history_item_p2 = SearchHistory.query.filter_by(user_id=current_user.id, product_name=product2_name).first()
            if history_item_p2:
                history_item_p2.search_time = datetime.utcnow()
                db.session.commit()

        logger.info(f"Also rendering comparison for product 2: '{product2_name}'")
        results2 = get_product_sentiment_analysis(product2_name)
    
    return render_template('results.html',
                           username=current_user.username,
                           product1_name=product1_name,
                           results1=results1,
                           product2_name=product2_name,
                           results2=results2)


# --- Authentication Routes ---
@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated: return redirect(url_for('index'))
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, email=form.email.data, password=form.password.data)
        db.session.add(user); db.session.commit()
        flash('Your account has been created! You can now log in.', 'success')
        return redirect(url_for('login'))
    return render_template('sign-up.html', form=form)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated: return redirect(url_for('index'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else: flash('Login Unsuccessful. Please check email and password.', 'danger')
    return render_template('sign-in.html', form=form)

@app.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('login'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')