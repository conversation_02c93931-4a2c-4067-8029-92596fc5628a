from flask import Flask, render_template, request, jsonify
from app_backend.sentiment_logic import get_product_sentiment_analysis
import os
from dotenv import load_dotenv

load_dotenv() # Load environment variables from .env file

app = Flask(__name__)

# Check if API Keys are set
if not os.getenv('TWITTER_BEARER_TOKEN'):
    app.logger.warning("TWITTER_BEARER_TOKEN environment variable is not set. Tweet fetching will fail.")
if not os.getenv('GOOGLE_API_KEY') or not os.getenv('GOOGLE_CSE_ID'):
    app.logger.warning("GOOGLE_API_KEY or GOOGLE_CSE_ID environment variables are not set. Image fetching will fail.")


@app.route('/')
def index():
    return render_template('sentiment_analysis.html')

@app.route('/analyze', methods=['POST'])
def analyze():
    try:
        data = request.get_json()
        product_name = data.get('product_name')
        # product_image_url is no longer sent from frontend

        if not product_name:
            return jsonify({'error': 'Product name is required'}), 400

        # Image URL will now be fetched in get_product_sentiment_analysis
        analysis_results = get_product_sentiment_analysis(product_name)
        
        response_data = {
            'product_name': product_name,
            'product_image_url': analysis_results['product_image_url'], # Fetched by backend
            'overall_sentiment': analysis_results['overall_sentiment'],
            'aspect_sentiments': analysis_results['aspect_sentiments'],
            'tweets_count': analysis_results['tweets_count'],
            'sample_tweets': analysis_results['sample_tweets'],
            'error_message': analysis_results.get('error_message')
        }
        return jsonify(response_data)

    except Exception as e:
        app.logger.error(f"Error during analysis: {e}", exc_info=True)
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5001)