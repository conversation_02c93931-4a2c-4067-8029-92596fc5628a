import os
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from dotenv import load_dotenv
from models import db, User, bcrypt, SearchHistory
from forms import RegistrationForm, LoginForm
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user
import logging
from datetime import datetime

# --- App Initialization ---
load_dotenv()
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'a_very_secret_and_random_key_for_sessions')
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URI')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# --- Initialize Extensions ---
db.init_app(app)
bcrypt.init_app(app)

# --- Flask-Login Setup ---
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login' 
login_manager.login_message_category = 'info' 

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# --- Configure Logging ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Create Database Tables ---
with app.app_context():
    logger.info("Creating database tables if they don't exist...")
    db.create_all()
    logger.info("Database tables check complete.")


# --- Main Application Routes ---
@app.route('/')
@login_required 
def index():
    search_history = db.session.query(SearchHistory.product_name)\
        .filter_by(user_id=current_user.id)\
        .distinct()\
        .order_by(db.func.max(SearchHistory.search_time).desc())\
        .group_by(SearchHistory.product_name)\
        .limit(10).all()
    history_items = [item[0] for item in search_history]
    return render_template('sentiment_analysis.html', username=current_user.username, history=history_items)

@app.route('/analyze', methods=['POST'])
@login_required
def analyze():
    from app_backend.sentiment_logic import get_product_sentiment_analysis
    try:
        data = request.get_json()
        product_name = data.get('product_name')

        if not product_name or not product_name.strip():
            return jsonify({'error': 'Product name is required.'}), 400

        last_search = SearchHistory.query.filter_by(user_id=current_user.id).order_by(SearchHistory.search_time.desc()).first()
        if not last_search or last_search.product_name.lower() != product_name.lower():
            new_search = SearchHistory(product_name=product_name.strip(), user_id=current_user.id)
            db.session.add(new_search)
            db.session.commit()
            logger.info(f"Saved search '{product_name}' for user '{current_user.username}'.")
        else:
            last_search.search_time = datetime.utcnow()
            db.session.commit()
            logger.info(f"Updated timestamp for search '{product_name}' for user '{current_user.username}'.")

        analysis_results = get_product_sentiment_analysis(product_name)

        response_data = {
            'product_name': product_name,
            'product_image_url': analysis_results.get('product_image_url'),
            'product_specifications_snippet': analysis_results.get('product_specifications_snippet', "Could not fetch specifications."),
            'overall_sentiment': analysis_results.get('overall_sentiment', {}),
            'aspect_sentiments': analysis_results.get('aspect_sentiments', {}),
            'tweets_count': analysis_results.get('tweets_count', 0),
            'sample_tweets': analysis_results.get('sample_tweets', []),
            'error_message': analysis_results.get('error_message')
        }
        return jsonify(response_data)

    except Exception as e:
        app.logger.error(f"Error during analysis: {e}", exc_info=True)
        return jsonify({'error': str(e)}), 500

# --- NEW ROUTE FOR RENDERING THE COMPARISON PAGE ---
@app.route('/compare_page')
@login_required
def compare_page():
    # This route just renders the page. The data will be fetched by JavaScript.
    product1 = request.args.get('product1', 'Product 1')
    product2 = request.args.get('product2', 'Product 2')
    return render_template('compare.html', product1=product1, product2=product2, username=current_user.username)


# --- NEW API ENDPOINT FOR GETTING COMPARISON DATA ---
@app.route('/compare_data')
@login_required
def compare_data():
    from app_backend.sentiment_logic import get_product_sentiment_analysis
    
    product1_name = request.args.get('product1')
    product2_name = request.args.get('product2')

    if not product1_name or not product2_name:
        return jsonify({'error': 'Two product names are required for comparison.'}), 400

    logger.info(f"Initiating comparison for '{product1_name}' vs '{product2_name}' for user '{current_user.username}'")
    
    # Run analysis for both products
    results1 = get_product_sentiment_analysis(product1_name)
    results2 = get_product_sentiment_analysis(product2_name)
    
    comparison_data = {
        'product1': results1,
        'product2': results2
    }
    
    return jsonify(comparison_data)

# --- Authentication Routes ---
@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, email=form.email.data, password=form.password.data)
        db.session.add(user)
        db.session.commit()
        flash('Your account has been created! You are now able to log in.', 'success')
        logger.info(f"New user created: {form.username.data}")
        return redirect(url_for('login'))
    return render_template('sign-up.html', form=form)


@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            next_page = request.args.get('next')
            logger.info(f"User logged in: {user.username}")
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash('Login Unsuccessful. Please check email and password.', 'danger')
    return render_template('sign-in.html', form=form)


@app.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('login'))


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')